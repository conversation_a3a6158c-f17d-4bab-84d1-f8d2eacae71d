# Dapper ORM Integration Guide

This document provides a comprehensive guide to the Dapper ORM integration in the Bookify application, following best practices and complementing the existing Entity Framework Core setup.

## Overview

The Bookify application uses a hybrid approach:
- **Entity Framework Core**: For write operations, complex domain logic, and migrations
- **Dapper**: For read-heavy operations, complex queries, and performance-critical scenarios

This approach follows the CQRS (Command Query Responsibility Segregation) pattern, where:
- Commands use EF Core for consistency and domain logic
- Queries use Dapper for performance and flexibility

## Architecture

### Core Components

1. **SqlConnectionFactory**: Enhanced connection factory with retry logic and monitoring
2. **DapperRepository**: Base repository with common CRUD operations
3. **Query Services**: Specialized services for complex read operations
4. **Type Handlers**: Custom handlers for domain value objects
5. **Performance Monitor**: Monitoring and logging for Dapper operations
6. **Extensions**: Utility methods for bulk operations and dynamic queries

### Project Structure

```
src/Bookify.Infrastructure/Data/
├── SqlConnectionFactory.cs              # Enhanced connection factory
├── DapperRepository.cs                  # Base repository pattern
├── DateOnlyTypeHandler.cs               # Type handlers for custom types
├── QueryServices/
│   ├── BookingQueryService.cs           # Booking-specific queries
│   └── ApartmentQueryService.cs         # Apartment-specific queries
├── Extensions/
│   └── DapperExtensions.cs              # Utility extensions
└── Monitoring/
    └── DapperPerformanceMonitor.cs      # Performance monitoring
```

## Getting Started

### 1. Basic Query Service Usage

```csharp
public class GetBookingDetailsQueryHandler : IQueryHandler<GetBookingDetailsQuery, BookingDetailsResponse>
{
    private readonly IBookingQueryService _bookingQueryService;

    public GetBookingDetailsQueryHandler(IBookingQueryService bookingQueryService)
    {
        _bookingQueryService = bookingQueryService;
    }

    public async Task<Result<BookingDetailsResponse>> Handle(GetBookingDetailsQuery request, CancellationToken cancellationToken)
    {
        var bookingDetails = await _bookingQueryService.GetBookingDetailsAsync(request.BookingId, cancellationToken);
        
        if (bookingDetails == null)
            return Result.Failure<BookingDetailsResponse>(BookingErrors.NotFound);

        return Result.Success(new BookingDetailsResponse(bookingDetails));
    }
}
```

### 2. Custom Repository Implementation

```csharp
public class CustomReportRepository : DapperRepository
{
    public CustomReportRepository(ISqlConnectionFactory connectionFactory) : base(connectionFactory)
    {
    }

    public async Task<IEnumerable<RevenueReportDto>> GetRevenueReportAsync(DateOnly fromDate, DateOnly toDate)
    {
        const string sql = """
            SELECT 
                DATE_TRUNC('month', b.created_on_utc) AS Month,
                COUNT(*) AS BookingCount,
                SUM(b.total_price_amount) AS TotalRevenue
            FROM bookings b
            WHERE b.created_on_utc >= @FromDate 
              AND b.created_on_utc <= @ToDate
              AND b.status = @CompletedStatus
            GROUP BY DATE_TRUNC('month', b.created_on_utc)
            ORDER BY Month
            """;

        return await QueryAsync<RevenueReportDto>(sql, new { FromDate = fromDate, ToDate = toDate, CompletedStatus = 4 });
    }
}
```

### 3. Dynamic Query Building

```csharp
public async Task<IEnumerable<BookingDto>> SearchBookingsAsync(BookingSearchCriteria criteria)
{
    var queryBuilder = new DynamicQueryBuilder()
        .Select("b.id, b.status, b.total_price_amount, u.email, a.name")
        .From("bookings b")
        .Join("INNER JOIN users u ON b.user_id = u.id")
        .Join("INNER JOIN apartments a ON b.apartment_id = a.id")
        .WhereIf(criteria.UserId.HasValue, "b.user_id = ?", criteria.UserId)
        .WhereIf(criteria.Status.HasValue, "b.status = ?", criteria.Status)
        .OrderBy("b.created_on_utc DESC")
        .Limit(criteria.PageSize ?? 50, criteria.PageNumber);

    var (sql, parameters) = queryBuilder.Build();
    return await QueryAsync<BookingDto>(sql, parameters);
}
```

## Advanced Features

### 1. Performance Monitoring

All Dapper operations can be monitored for performance:

```csharp
public class MonitoredBookingService
{
    private readonly IDapperPerformanceMonitor _monitor;
    private readonly ISqlConnectionFactory _connectionFactory;

    public async Task<IEnumerable<BookingDto>> GetBookingsAsync(Guid userId)
    {
        return await _monitor.MonitorQueryAsync(
            "GetUserBookings",
            "SELECT * FROM bookings WHERE user_id = @UserId",
            async () =>
            {
                using var connection = _connectionFactory.CreateConnection();
                return await connection.QueryAsync<BookingDto>("SELECT * FROM bookings WHERE user_id = @UserId", new { UserId = userId });
            },
            new { UserId = userId }
        );
    }
}
```

### 2. Bulk Operations

```csharp
// Bulk insert
await connection.BulkInsertAsync("booking_notifications", notifications);

// Bulk update
await connection.BulkUpdateAsync("bookings", bookingUpdates, "id");

// Bulk delete
await connection.BulkDeleteAsync("temp_bookings", "id", expiredBookingIds);
```

### 3. Transaction Support

```csharp
await DatabaseUtilities.ExecuteInTransactionAsync(connection, async (conn, transaction) =>
{
    // Multiple operations in a single transaction
    await conn.ExecuteAsync("UPDATE bookings SET status = @Status WHERE id = @Id", updateData, transaction);
    await conn.ExecuteAsync("INSERT INTO booking_audit (booking_id, action) VALUES (@BookingId, @Action)", auditData, transaction);
});
```

### 4. Retry Logic

```csharp
// Query with automatic retry for transient failures
var booking = await connection.QueryWithRetryAsync<BookingDto>(
    "SELECT * FROM bookings WHERE id = @Id",
    new { Id = bookingId },
    maxRetries: 3,
    retryDelay: TimeSpan.FromMilliseconds(500)
);
```

## Type Handlers

Custom type handlers are automatically registered for domain value objects:

- **DateOnlyTypeHandler**: Handles `DateOnly` types
- **CurrencyTypeHandler**: Handles `Currency` value objects
- **MoneyAmountTypeHandler**: Handles decimal amounts for `Money` objects

### Custom Type Handler Example

```csharp
public class CustomValueObjectTypeHandler : SqlMapper.TypeHandler<CustomValueObject>
{
    public override CustomValueObject Parse(object value)
    {
        return CustomValueObject.FromString(value.ToString());
    }

    public override void SetValue(IDbDataParameter parameter, CustomValueObject value)
    {
        parameter.DbType = DbType.String;
        parameter.Value = value.ToString();
    }
}

// Register in DependencyInjection.cs
SqlMapper.AddTypeHandler(new CustomValueObjectTypeHandler());
```

## Best Practices

### 1. Query Organization

- Use specialized query services for domain-specific operations
- Keep complex queries in dedicated repository classes
- Use the base `DapperRepository` for common operations

### 2. Performance

- Use `QueryFirstOrDefaultAsync` for single results
- Use `QueryAsync` for multiple results
- Monitor slow queries using the performance monitor
- Use bulk operations for large data sets

### 3. Error Handling

- Wrap operations in try-catch blocks
- Use retry logic for transient failures
- Log errors with appropriate context

### 4. Testing

- Use the existing integration test infrastructure
- Test query services with real database operations
- Verify performance characteristics in tests

## Configuration

### Connection String

The same connection string used by Entity Framework Core is shared with Dapper:

```json
{
  "ConnectionStrings": {
    "Database": "Host=localhost;Port=5432;Database=bookify;Username=postgres;Password=password;"
  }
}
```

### Dependency Injection

All Dapper services are automatically registered:

```csharp
// In DependencyInjection.cs
services.AddSingleton<ISqlConnectionFactory, SqlConnectionFactory>();
services.AddScoped<IBookingQueryService, BookingQueryService>();
services.AddScoped<IApartmentQueryService, ApartmentQueryService>();
services.AddSingleton<IDapperPerformanceMonitor, DapperPerformanceMonitor>();
```

## Monitoring and Diagnostics

### Performance Metrics

Access performance metrics:

```csharp
var metrics = performanceMonitor.GetMetrics();
Console.WriteLine($"Total Queries: {metrics.TotalQueries}");
Console.WriteLine($"Average Duration: {metrics.AverageQueryDurationMs}ms");
Console.WriteLine($"Success Rate: {metrics.QuerySuccessRate}%");
```

### Logging

Dapper operations are logged at different levels:
- **Debug**: Query execution times
- **Trace**: Full query details with parameters
- **Warning**: Slow queries (>1 second)
- **Error**: Query failures

## Migration from EF Core Queries

When migrating read operations from EF Core to Dapper:

1. Identify performance-critical queries
2. Create corresponding Dapper query methods
3. Update query handlers to use Dapper services
4. Add integration tests
5. Monitor performance improvements

## Troubleshooting

### Common Issues

1. **Type Mapping Errors**: Ensure custom type handlers are registered
2. **Connection Issues**: Check connection string and network connectivity
3. **Performance Issues**: Use the performance monitor to identify slow queries
4. **Transaction Issues**: Ensure proper transaction scope and disposal

### Debugging

Enable detailed logging in `appsettings.json`:

```json
{
  "Logging": {
    "LogLevel": {
      "Bookify.Infrastructure.Data": "Debug",
      "Dapper": "Debug"
    }
  }
}
```

## Examples

See `src/Bookify.Application/Examples/DapperUsageExamples.cs` for comprehensive usage examples including:
- Complex query implementations
- Performance monitoring
- Bulk operations
- Transaction handling
- Error handling patterns

## Contributing

When adding new Dapper functionality:

1. Follow the existing patterns and conventions
2. Add appropriate error handling and logging
3. Include integration tests
4. Update this documentation
5. Consider performance implications
