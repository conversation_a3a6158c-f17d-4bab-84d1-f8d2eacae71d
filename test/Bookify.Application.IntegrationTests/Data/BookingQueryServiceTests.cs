using Bookify.Application.IntegrationTests.Infrastructure;
using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Apartments.Enums;
using Bookify.Domain.Entities.Apartments.ValueObjects;
using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.Enums;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Shared;
using Bookify.Infrastructure;
using Bookify.Infrastructure.Data.QueryServices;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;

namespace Bookify.Application.IntegrationTests.Data;

public class BookingQueryServiceTests : BaseIntegrationTest
{
    private readonly IBookingQueryService _bookingQueryService;

    public BookingQueryServiceTests(IntegrationTestWebAppFactory factory) : base(factory)
    {
        _bookingQueryService = Scope.ServiceProvider.GetRequiredService<IBookingQueryService>();
    }

    [Fact]
    public async Task GetBookingDetailsAsync_WhenBookingExists_ShouldReturnBookingDetails()
    {
        // Arrange
        var user = CreateTestUser();
        var apartment = CreateTestApartment();
        var booking = CreateTestBooking(apartment.Id, user.Id);

        await AddToDbContextAsync(user, apartment, booking);

        // Act
        var result = await _bookingQueryService.GetBookingDetailsAsync(booking.Id.Value);

        // Assert
        result.Should().NotBeNull();
        result!.BookingId.Should().Be(booking.Id.Value);
        result.Status.Should().Be(booking.Status);
        result.User.Should().NotBeNull();
        result.User.UserEmail.Should().Be(user.Email.Value);
        result.Apartment.Should().NotBeNull();
        result.Apartment.ApartmentName.Should().Be(apartment.Name);
    }

    [Fact]
    public async Task GetBookingDetailsAsync_WhenBookingDoesNotExist_ShouldReturnNull()
    {
        // Arrange
        var nonExistentBookingId = Guid.NewGuid();

        // Act
        var result = await _bookingQueryService.GetBookingDetailsAsync(nonExistentBookingId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetUserBookingsAsync_WhenUserHasBookings_ShouldReturnBookings()
    {
        // Arrange
        var user = CreateTestUser();
        var apartment1 = CreateTestApartment();
        var apartment2 = CreateTestApartment();
        var booking1 = CreateTestBooking(apartment1.Id, user.Id, BookingStatus.Reserved);
        var booking2 = CreateTestBooking(apartment2.Id, user.Id, BookingStatus.Confirmed);

        await AddToDbContextAsync(user, apartment1, apartment2, booking1, booking2);

        // Act
        var result = await _bookingQueryService.GetUserBookingsAsync(user.Id.Value);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(b => b.BookingId == booking1.Id.Value);
        result.Should().Contain(b => b.BookingId == booking2.Id.Value);
    }

    [Fact]
    public async Task GetUserBookingsAsync_WithStatusFilter_ShouldReturnFilteredBookings()
    {
        // Arrange
        var user = CreateTestUser();
        var apartment1 = CreateTestApartment();
        var apartment2 = CreateTestApartment();
        var booking1 = CreateTestBooking(apartment1.Id, user.Id, BookingStatus.Reserved);
        var booking2 = CreateTestBooking(apartment2.Id, user.Id, BookingStatus.Confirmed);

        await AddToDbContextAsync(user, apartment1, apartment2, booking1, booking2);

        // Act
        var result = await _bookingQueryService.GetUserBookingsAsync(user.Id.Value, BookingStatus.Reserved);

        // Assert
        result.Should().HaveCount(1);
        result.First().BookingId.Should().Be(booking1.Id.Value);
        result.First().Status.Should().Be(BookingStatus.Reserved);
    }

    [Fact]
    public async Task GetBookingStatisticsAsync_WhenBookingsExist_ShouldReturnCorrectStatistics()
    {
        // Arrange
        var user = CreateTestUser();
        var apartment = CreateTestApartment();
        var reservedBooking = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Reserved);
        var confirmedBooking = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Confirmed);
        var completedBooking = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Completed);

        await AddToDbContextAsync(user, apartment, reservedBooking, confirmedBooking, completedBooking);

        // Act
        var result = await _bookingQueryService.GetBookingStatisticsAsync();

        // Assert
        result.Should().NotBeNull();
        result.TotalBookings.Should().Be(3);
        result.ReservedCount.Should().Be(1);
        result.ConfirmedCount.Should().Be(1);
        result.CompletedCount.Should().Be(1);
        result.TotalRevenue.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetBookingConflictsAsync_WhenConflictsExist_ShouldReturnConflicts()
    {
        // Arrange
        var user = CreateTestUser();
        var apartment = CreateTestApartment();
        var existingBooking = CreateTestBooking(
            apartment.Id, 
            user.Id, 
            BookingStatus.Confirmed,
            DateOnly.FromDateTime(DateTime.Today.AddDays(5)),
            DateOnly.FromDateTime(DateTime.Today.AddDays(10))
        );

        await AddToDbContextAsync(user, apartment, existingBooking);

        // Act - Check for conflicts with overlapping dates
        var result = await _bookingQueryService.GetBookingConflictsAsync(
            apartment.Id.Value,
            DateOnly.FromDateTime(DateTime.Today.AddDays(7)),
            DateOnly.FromDateTime(DateTime.Today.AddDays(12))
        );

        // Assert
        result.Should().HaveCount(1);
        result.First().BookingId.Should().Be(existingBooking.Id.Value);
    }

    [Fact]
    public async Task GetTotalRevenueAsync_WhenCompletedBookingsExist_ShouldReturnCorrectRevenue()
    {
        // Arrange
        var user = CreateTestUser();
        var apartment = CreateTestApartment();
        var completedBooking1 = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Completed);
        var completedBooking2 = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Completed);
        var reservedBooking = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Reserved);

        await AddToDbContextAsync(user, apartment, completedBooking1, completedBooking2, reservedBooking);

        // Act
        var result = await _bookingQueryService.GetTotalRevenueAsync();

        // Assert
        result.Should().BeGreaterThan(0);
        // Revenue should only include completed bookings
        var expectedRevenue = completedBooking1.TotalPrice.Amount + completedBooking2.TotalPrice.Amount;
        result.Should().Be(expectedRevenue);
    }

    private static User CreateTestUser()
    {
        return User.Create(
            FirstName.Create("John").Value,
            LastName.Create("Doe").Value,
            Email.Create($"john.doe.{Guid.NewGuid()}@test.com").Value
        );
    }

    private static Apartment CreateTestApartment()
    {
        return new Apartment(
            ApartmentId.New(),
            $"Test Apartment {Guid.NewGuid()}",
            "Test Description",
            new Address("USA", "CA", "90210", "Los Angeles", "123 Test St"),
            new Money(100m, Currency.Usd),
            new Money(25m, Currency.Usd),
            new List<Amenity> { Amenity.Parking, Amenity.AirConditioning }
        );
    }

    private static Booking CreateTestBooking(
        ApartmentId apartmentId, 
        UserId userId, 
        BookingStatus status = BookingStatus.Reserved,
        DateOnly? startDate = null,
        DateOnly? endDate = null)
    {
        var start = startDate ?? DateOnly.FromDateTime(DateTime.Today.AddDays(1));
        var end = endDate ?? DateOnly.FromDateTime(DateTime.Today.AddDays(3));
        var duration = DateRange.Create(start, end);
        
        var priceForPeriod = new Money(200m, Currency.Usd);
        var cleaningFee = new Money(50m, Currency.Usd);
        var amenitiesUpCharge = new Money(20m, Currency.Usd);
        var totalPrice = new Money(270m, Currency.Usd);

        var booking = Booking.Reserve(
            new Apartment(
                apartmentId,
                "Test Apartment",
                "Description",
                new Address("USA", "CA", "90210", "LA", "123 St"),
                new Money(100m, Currency.Usd),
                cleaningFee,
                new List<Amenity>()
            ),
            userId,
            duration,
            DateTime.UtcNow,
            new TestPricingService()
        );

        // Use reflection to set the status if different from Reserved
        if (status != BookingStatus.Reserved)
        {
            var statusProperty = typeof(Booking).GetProperty("Status");
            statusProperty?.SetValue(booking, status);
        }

        return booking;
    }

    private async Task AddToDbContextAsync(params object[] entities)
    {
        var dbContext = Scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        foreach (var entity in entities)
        {
            dbContext.Add(entity);
        }
        
        await dbContext.SaveChangesAsync();
    }
}


