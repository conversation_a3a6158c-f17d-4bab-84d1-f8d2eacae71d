using Bookify.Application.IntegrationTests.Infrastructure;
using Bookify.Domain.Entities.Apartments;
using Bookify.Domain.Entities.Apartments.Enums;
using Bookify.Domain.Entities.Apartments.ValueObjects;
using Bookify.Domain.Entities.Bookings;
using Bookify.Domain.Entities.Bookings.Enums;
using Bookify.Domain.Entities.Bookings.ValueObjects;
using Bookify.Domain.Entities.Users;
using Bookify.Domain.Shared;
using Bookify.Infrastructure;
using Bookify.Infrastructure.Data.QueryServices;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;

namespace Bookify.Application.IntegrationTests.Data;

public class ApartmentQueryServiceTests : BaseIntegrationTest
{
    private readonly IApartmentQueryService _apartmentQueryService;

    public ApartmentQueryServiceTests(IntegrationTestWebAppFactory factory) : base(factory)
    {
        _apartmentQueryService = Scope.ServiceProvider.GetRequiredService<IApartmentQueryService>();
    }

    [Fact]
    public async Task GetApartmentDetailsAsync_WhenApartmentExists_ShouldReturnApartmentDetails()
    {
        // Arrange
        var apartment = CreateTestApartment("Luxury Apartment", "New York", "NY");
        await AddToDbContextAsync(apartment);

        // Act
        var result = await _apartmentQueryService.GetApartmentDetailsAsync(apartment.Id.Value);

        // Assert
        result.Should().NotBeNull();
        result!.ApartmentId.Should().Be(apartment.Id.Value);
        result.Name.Should().Be(apartment.Name);
        result.City.Should().Be("New York");
        result.State.Should().Be("NY");
    }

    [Fact]
    public async Task GetApartmentDetailsAsync_WhenApartmentDoesNotExist_ShouldReturnNull()
    {
        // Arrange
        var nonExistentApartmentId = Guid.NewGuid();

        // Act
        var result = await _apartmentQueryService.GetApartmentDetailsAsync(nonExistentApartmentId);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task SearchApartmentsAsync_WithLocationFilter_ShouldReturnMatchingApartments()
    {
        // Arrange
        var apartment1 = CreateTestApartment("NYC Apartment", "New York", "NY");
        var apartment2 = CreateTestApartment("LA Apartment", "Los Angeles", "CA");
        var apartment3 = CreateTestApartment("NYC Loft", "New York", "NY");

        await AddToDbContextAsync(apartment1, apartment2, apartment3);

        var criteria = new ApartmentSearchCriteria
        {
            City = "New York"
        };

        // Act
        var result = await _apartmentQueryService.SearchApartmentsAsync(criteria);

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(a => a.ApartmentId == apartment1.Id.Value);
        result.Should().Contain(a => a.ApartmentId == apartment3.Id.Value);
        result.Should().NotContain(a => a.ApartmentId == apartment2.Id.Value);
    }

    [Fact]
    public async Task SearchApartmentsAsync_WithPriceRange_ShouldReturnMatchingApartments()
    {
        // Arrange
        var cheapApartment = CreateTestApartment("Budget Place", "City", "State", 50m);
        var midRangeApartment = CreateTestApartment("Mid Range", "City", "State", 150m);
        var expensiveApartment = CreateTestApartment("Luxury", "City", "State", 300m);

        await AddToDbContextAsync(cheapApartment, midRangeApartment, expensiveApartment);

        var criteria = new ApartmentSearchCriteria
        {
            MinPrice = 100m,
            MaxPrice = 200m
        };

        // Act
        var result = await _apartmentQueryService.SearchApartmentsAsync(criteria);

        // Assert
        result.Should().HaveCount(1);
        result.First().ApartmentId.Should().Be(midRangeApartment.Id.Value);
    }

    [Fact]
    public async Task SearchApartmentsAsync_WithAmenityFilter_ShouldReturnMatchingApartments()
    {
        // Arrange
        var apartmentWithParking = CreateTestApartment("With Parking", "City", "State", 100m, 
            new List<Amenity> { Amenity.Parking, Amenity.AirConditioning });
        var apartmentWithoutParking = CreateTestApartment("No Parking", "City", "State", 100m, 
            new List<Amenity> { Amenity.AirConditioning });

        await AddToDbContextAsync(apartmentWithParking, apartmentWithoutParking);

        var criteria = new ApartmentSearchCriteria
        {
            RequiredAmenities = new List<Amenity> { Amenity.Parking }
        };

        // Act
        var result = await _apartmentQueryService.SearchApartmentsAsync(criteria);

        // Assert
        result.Should().HaveCount(1);
        result.First().ApartmentId.Should().Be(apartmentWithParking.Id.Value);
    }

    [Fact]
    public async Task SearchApartmentsAsync_WithAvailabilityFilter_ShouldReturnAvailableApartments()
    {
        // Arrange
        var user = CreateTestUser();
        var availableApartment = CreateTestApartment("Available", "City", "State");
        var bookedApartment = CreateTestApartment("Booked", "City", "State");

        // Create a booking that conflicts with our search dates
        var booking = CreateTestBooking(
            bookedApartment.Id, 
            user.Id, 
            BookingStatus.Confirmed,
            DateOnly.FromDateTime(DateTime.Today.AddDays(5)),
            DateOnly.FromDateTime(DateTime.Today.AddDays(10))
        );

        await AddToDbContextAsync(user, availableApartment, bookedApartment, booking);

        var criteria = new ApartmentSearchCriteria
        {
            StartDate = DateOnly.FromDateTime(DateTime.Today.AddDays(7)),
            EndDate = DateOnly.FromDateTime(DateTime.Today.AddDays(12))
        };

        // Act
        var result = await _apartmentQueryService.SearchApartmentsAsync(criteria);

        // Assert
        result.Should().HaveCount(1);
        result.First().ApartmentId.Should().Be(availableApartment.Id.Value);
    }

    [Fact]
    public async Task GetApartmentAvailabilityAsync_ShouldReturnCorrectAvailability()
    {
        // Arrange
        var user = CreateTestUser();
        var apartment = CreateTestApartment("Test Apartment", "City", "State");
        
        // Create a booking for days 5-7
        var booking = CreateTestBooking(
            apartment.Id, 
            user.Id, 
            BookingStatus.Confirmed,
            DateOnly.FromDateTime(DateTime.Today.AddDays(5)),
            DateOnly.FromDateTime(DateTime.Today.AddDays(7))
        );

        await AddToDbContextAsync(user, apartment, booking);

        // Act - Check availability for days 1-10
        var result = await _apartmentQueryService.GetApartmentAvailabilityAsync(
            apartment.Id.Value,
            DateOnly.FromDateTime(DateTime.Today.AddDays(1)),
            DateOnly.FromDateTime(DateTime.Today.AddDays(10))
        );

        // Assert
        var availabilityList = result.ToList();
        availabilityList.Should().HaveCount(10);
        
        // Days 1-4 should be available
        availabilityList.Take(4).Should().AllSatisfy(a => a.IsAvailable.Should().BeTrue());
        
        // Days 5-7 should be unavailable (booked)
        availabilityList.Skip(4).Take(3).Should().AllSatisfy(a => a.IsAvailable.Should().BeFalse());
        
        // Days 8-10 should be available
        availabilityList.Skip(7).Take(3).Should().AllSatisfy(a => a.IsAvailable.Should().BeTrue());
    }

    [Fact]
    public async Task GetApartmentAnalyticsAsync_ShouldReturnCorrectAnalytics()
    {
        // Arrange
        var user = CreateTestUser();
        var apartment = CreateTestApartment("Analytics Test", "City", "State");
        
        var reservedBooking = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Reserved);
        var confirmedBooking = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Confirmed);
        var completedBooking = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Completed);
        var cancelledBooking = CreateTestBooking(apartment.Id, user.Id, BookingStatus.Cancelled);

        await AddToDbContextAsync(user, apartment, reservedBooking, confirmedBooking, completedBooking, cancelledBooking);

        // Act
        var result = await _apartmentQueryService.GetApartmentAnalyticsAsync(apartment.Id.Value);

        // Assert
        result.Should().NotBeNull();
        result.TotalBookings.Should().Be(4);
        result.ReservedBookings.Should().Be(1);
        result.ConfirmedBookings.Should().Be(1);
        result.CompletedBookings.Should().Be(1);
        result.CancelledBookings.Should().Be(1);
        result.TotalRevenue.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GetTopPerformingApartmentsAsync_ShouldReturnApartmentsByRevenue()
    {
        // Arrange
        var user = CreateTestUser();
        var highRevenueApartment = CreateTestApartment("High Revenue", "City", "State");
        var lowRevenueApartment = CreateTestApartment("Low Revenue", "City", "State");

        // Create multiple completed bookings for high revenue apartment
        var booking1 = CreateTestBooking(highRevenueApartment.Id, user.Id, BookingStatus.Completed);
        var booking2 = CreateTestBooking(highRevenueApartment.Id, user.Id, BookingStatus.Completed);
        
        // Create one completed booking for low revenue apartment
        var booking3 = CreateTestBooking(lowRevenueApartment.Id, user.Id, BookingStatus.Completed);

        await AddToDbContextAsync(user, highRevenueApartment, lowRevenueApartment, booking1, booking2, booking3);

        // Act
        var result = await _apartmentQueryService.GetTopPerformingApartmentsAsync(10);

        // Assert
        var apartments = result.ToList();
        apartments.Should().HaveCount(2);
        apartments.First().ApartmentId.Should().Be(highRevenueApartment.Id.Value);
        apartments.First().TotalRevenue.Should().BeGreaterThan(apartments.Last().TotalRevenue);
    }

    [Fact]
    public async Task GetApartmentsByLocationAsync_ShouldReturnApartmentsInLocation()
    {
        // Arrange
        var nyApartment1 = CreateTestApartment("NY Apt 1", "New York", "NY");
        var nyApartment2 = CreateTestApartment("NY Apt 2", "New York", "NY");
        var caApartment = CreateTestApartment("CA Apt", "Los Angeles", "CA");

        await AddToDbContextAsync(nyApartment1, nyApartment2, caApartment);

        // Act
        var result = await _apartmentQueryService.GetApartmentsByLocationAsync(state: "NY");

        // Assert
        result.Should().HaveCount(2);
        result.Should().Contain(a => a.ApartmentId == nyApartment1.Id.Value);
        result.Should().Contain(a => a.ApartmentId == nyApartment2.Id.Value);
        result.Should().NotContain(a => a.ApartmentId == caApartment.Id.Value);
    }

    private static User CreateTestUser()
    {
        return User.Create(
            FirstName.Create("John").Value,
            LastName.Create("Doe").Value,
            Email.Create($"john.doe.{Guid.NewGuid()}@test.com").Value
        );
    }

    private static Apartment CreateTestApartment(
        string name = "Test Apartment", 
        string city = "Test City", 
        string state = "Test State",
        decimal price = 100m,
        List<Amenity>? amenities = null)
    {
        return new Apartment(
            ApartmentId.New(),
            name,
            "Test Description",
            new Address("USA", state, "12345", city, "123 Test St"),
            new Money(price, Currency.Usd),
            new Money(25m, Currency.Usd),
            amenities ?? new List<Amenity> { Amenity.AirConditioning }
        );
    }

    private static Booking CreateTestBooking(
        ApartmentId apartmentId, 
        UserId userId, 
        BookingStatus status = BookingStatus.Reserved,
        DateOnly? startDate = null,
        DateOnly? endDate = null)
    {
        var start = startDate ?? DateOnly.FromDateTime(DateTime.Today.AddDays(1));
        var end = endDate ?? DateOnly.FromDateTime(DateTime.Today.AddDays(3));
        var duration = DateRange.Create(start, end);
        
        var apartment = new Apartment(
            apartmentId,
            "Test Apartment",
            "Description",
            new Address("USA", "CA", "90210", "LA", "123 St"),
            new Money(100m, Currency.Usd),
            new Money(25m, Currency.Usd),
            new List<Amenity>()
        );

        var booking = Booking.Reserve(
            apartment,
            userId,
            duration,
            DateTime.UtcNow,
            new TestPricingService()
        );

        // Use reflection to set the status if different from Reserved
        if (status != BookingStatus.Reserved)
        {
            var statusProperty = typeof(Booking).GetProperty("Status");
            statusProperty?.SetValue(booking, status);
        }

        return booking;
    }

    private async Task AddToDbContextAsync(params object[] entities)
    {
        var dbContext = Scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        
        foreach (var entity in entities)
        {
            dbContext.Add(entity);
        }
        
        await dbContext.SaveChangesAsync();
    }
}

// Test helper class
public class TestPricingService
{
    public PricingDetails CalculatePrice(Apartment apartment, DateRange period)
    {
        var currency = apartment.Price.Currency;
        var priceForPeriod = new Money(apartment.Price.Amount * period.LengthInDays, currency);
        var cleaningFee = apartment.CleaningFee;
        var amenitiesUpCharge = new Money(20m, currency);
        var totalPrice = priceForPeriod + cleaningFee + amenitiesUpCharge;

        return new PricingDetails(priceForPeriod, cleaningFee, amenitiesUpCharge, totalPrice);
    }
}
