using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace Bookify.Infrastructure.Data.Monitoring;

/// <summary>
/// Performance monitoring service for Dapper operations.
/// Provides logging, metrics collection, and performance analysis for database queries.
/// </summary>
public interface IDapperPerformanceMonitor
{
    Task<T> MonitorQueryAsync<T>(string operationName, string sql, Func<Task<T>> operation, object? parameters = null);
    Task MonitorCommandAsync(string operationName, string sql, Func<Task> operation, object? parameters = null);
    void LogSlowQuery(string sql, TimeSpan duration, object? parameters = null);
    void LogQueryError(string sql, Exception exception, object? parameters = null);
    PerformanceMetrics GetMetrics();
    void ResetMetrics();
}

internal sealed class DapperPerformanceMonitor : IDapperPerformanceMonitor
{
    private readonly ILogger<DapperPerformanceMonitor> _logger;
    private readonly PerformanceMetrics _metrics = new();
    private readonly TimeSpan _slowQueryThreshold = TimeSpan.FromMilliseconds(1000); // 1 second

    public DapperPerformanceMonitor(ILogger<DapperPerformanceMonitor> logger)
    {
        _logger = logger;
    }

    public async Task<T> MonitorQueryAsync<T>(string operationName, string sql, Func<Task<T>> operation, object? parameters = null)
    {
        var stopwatch = Stopwatch.StartNew();
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogDebug("Starting query operation: {OperationName}", operationName);
            
            var result = await operation();
            
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;

            // Update metrics
            _metrics.RecordQuery(duration, true);

            // Log performance information
            LogQueryPerformance(operationName, sql, duration, parameters);

            // Check for slow queries
            if (duration > _slowQueryThreshold)
            {
                LogSlowQuery(sql, duration, parameters);
            }

            _logger.LogDebug("Completed query operation: {OperationName} in {Duration}ms", 
                operationName, duration.TotalMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;

            // Update metrics
            _metrics.RecordQuery(duration, false);

            // Log error
            LogQueryError(sql, ex, parameters);

            _logger.LogError(ex, "Failed query operation: {OperationName} after {Duration}ms", 
                operationName, duration.TotalMilliseconds);

            throw;
        }
    }

    public async Task MonitorCommandAsync(string operationName, string sql, Func<Task> operation, object? parameters = null)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogDebug("Starting command operation: {OperationName}", operationName);
            
            await operation();
            
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;

            // Update metrics
            _metrics.RecordCommand(duration, true);

            // Log performance information
            LogCommandPerformance(operationName, sql, duration, parameters);

            // Check for slow commands
            if (duration > _slowQueryThreshold)
            {
                LogSlowQuery(sql, duration, parameters);
            }

            _logger.LogDebug("Completed command operation: {OperationName} in {Duration}ms", 
                operationName, duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;

            // Update metrics
            _metrics.RecordCommand(duration, false);

            // Log error
            LogQueryError(sql, ex, parameters);

            _logger.LogError(ex, "Failed command operation: {OperationName} after {Duration}ms", 
                operationName, duration.TotalMilliseconds);

            throw;
        }
    }

    public void LogSlowQuery(string sql, TimeSpan duration, object? parameters = null)
    {
        _metrics.RecordSlowQuery();

        _logger.LogWarning("Slow query detected: {Duration}ms\nSQL: {Sql}\nParameters: {@Parameters}", 
            duration.TotalMilliseconds, sql, parameters);
    }

    public void LogQueryError(string sql, Exception exception, object? parameters = null)
    {
        _logger.LogError(exception, "Query error occurred\nSQL: {Sql}\nParameters: {@Parameters}", 
            sql, parameters);
    }

    public PerformanceMetrics GetMetrics()
    {
        return _metrics.Clone();
    }

    public void ResetMetrics()
    {
        _metrics.Reset();
    }

    private void LogQueryPerformance(string operationName, string sql, TimeSpan duration, object? parameters)
    {
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug("Query Performance - Operation: {OperationName}, Duration: {Duration}ms\nSQL: {Sql}", 
                operationName, duration.TotalMilliseconds, sql);
        }

        if (_logger.IsEnabled(LogLevel.Trace))
        {
            _logger.LogTrace("Query Details - Operation: {OperationName}, Duration: {Duration}ms\nSQL: {Sql}\nParameters: {@Parameters}", 
                operationName, duration.TotalMilliseconds, sql, parameters);
        }
    }

    private void LogCommandPerformance(string operationName, string sql, TimeSpan duration, object? parameters)
    {
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug("Command Performance - Operation: {OperationName}, Duration: {Duration}ms\nSQL: {Sql}", 
                operationName, duration.TotalMilliseconds, sql);
        }

        if (_logger.IsEnabled(LogLevel.Trace))
        {
            _logger.LogTrace("Command Details - Operation: {OperationName}, Duration: {Duration}ms\nSQL: {Sql}\nParameters: {@Parameters}", 
                operationName, duration.TotalMilliseconds, sql, parameters);
        }
    }
}

/// <summary>
/// Performance metrics for Dapper operations.
/// </summary>
public class PerformanceMetrics
{
    private readonly object _lock = new();
    private long _totalQueries;
    private long _successfulQueries;
    private long _failedQueries;
    private long _totalCommands;
    private long _successfulCommands;
    private long _failedCommands;
    private long _slowQueries;
    private double _totalQueryDurationMs;
    private double _totalCommandDurationMs;
    private double _minQueryDurationMs = double.MaxValue;
    private double _maxQueryDurationMs;
    private double _minCommandDurationMs = double.MaxValue;
    private double _maxCommandDurationMs;

    public long TotalQueries => _totalQueries;
    public long SuccessfulQueries => _successfulQueries;
    public long FailedQueries => _failedQueries;
    public long TotalCommands => _totalCommands;
    public long SuccessfulCommands => _successfulCommands;
    public long FailedCommands => _failedCommands;
    public long SlowQueries => _slowQueries;
    public double AverageQueryDurationMs => _totalQueries > 0 ? _totalQueryDurationMs / _totalQueries : 0;
    public double AverageCommandDurationMs => _totalCommands > 0 ? _totalCommandDurationMs / _totalCommands : 0;
    public double MinQueryDurationMs => _minQueryDurationMs == double.MaxValue ? 0 : _minQueryDurationMs;
    public double MaxQueryDurationMs => _maxQueryDurationMs;
    public double MinCommandDurationMs => _minCommandDurationMs == double.MaxValue ? 0 : _minCommandDurationMs;
    public double MaxCommandDurationMs => _maxCommandDurationMs;
    public double QuerySuccessRate => _totalQueries > 0 ? (double)_successfulQueries / _totalQueries * 100 : 0;
    public double CommandSuccessRate => _totalCommands > 0 ? (double)_successfulCommands / _totalCommands * 100 : 0;

    public void RecordQuery(TimeSpan duration, bool success)
    {
        lock (_lock)
        {
            _totalQueries++;
            var durationMs = duration.TotalMilliseconds;
            _totalQueryDurationMs += durationMs;

            if (durationMs < _minQueryDurationMs)
                _minQueryDurationMs = durationMs;
            if (durationMs > _maxQueryDurationMs)
                _maxQueryDurationMs = durationMs;

            if (success)
                _successfulQueries++;
            else
                _failedQueries++;
        }
    }

    public void RecordCommand(TimeSpan duration, bool success)
    {
        lock (_lock)
        {
            _totalCommands++;
            var durationMs = duration.TotalMilliseconds;
            _totalCommandDurationMs += durationMs;

            if (durationMs < _minCommandDurationMs)
                _minCommandDurationMs = durationMs;
            if (durationMs > _maxCommandDurationMs)
                _maxCommandDurationMs = durationMs;

            if (success)
                _successfulCommands++;
            else
                _failedCommands++;
        }
    }

    public void RecordSlowQuery()
    {
        lock (_lock)
        {
            _slowQueries++;
        }
    }

    public void Reset()
    {
        lock (_lock)
        {
            _totalQueries = 0;
            _successfulQueries = 0;
            _failedQueries = 0;
            _totalCommands = 0;
            _successfulCommands = 0;
            _failedCommands = 0;
            _slowQueries = 0;
            _totalQueryDurationMs = 0;
            _totalCommandDurationMs = 0;
            _minQueryDurationMs = double.MaxValue;
            _maxQueryDurationMs = 0;
            _minCommandDurationMs = double.MaxValue;
            _maxCommandDurationMs = 0;
        }
    }

    public PerformanceMetrics Clone()
    {
        lock (_lock)
        {
            return new PerformanceMetrics
            {
                _totalQueries = _totalQueries,
                _successfulQueries = _successfulQueries,
                _failedQueries = _failedQueries,
                _totalCommands = _totalCommands,
                _successfulCommands = _successfulCommands,
                _failedCommands = _failedCommands,
                _slowQueries = _slowQueries,
                _totalQueryDurationMs = _totalQueryDurationMs,
                _totalCommandDurationMs = _totalCommandDurationMs,
                _minQueryDurationMs = _minQueryDurationMs,
                _maxQueryDurationMs = _maxQueryDurationMs,
                _minCommandDurationMs = _minCommandDurationMs,
                _maxCommandDurationMs = _maxCommandDurationMs
            };
        }
    }

    public override string ToString()
    {
        return $"Queries: {TotalQueries} (Success: {QuerySuccessRate:F1}%, Avg: {AverageQueryDurationMs:F2}ms), " +
               $"Commands: {TotalCommands} (Success: {CommandSuccessRate:F1}%, Avg: {AverageCommandDurationMs:F2}ms), " +
               $"Slow Queries: {SlowQueries}";
    }
}
