using Bookify.Domain.Shared;
using Dapper;
using System.Data;

namespace Bookify.Infrastructure.Data;

/// <summary>
/// Because I'm using Date Only types in the date range value objects, this is needed to tell dapper how to be able map this type,
/// because it doesn't support it out of the box.
/// </summary>
internal sealed class DateOnlyTypeHandler : SqlMapper.TypeHandler<DateOnly>
{
    public override DateOnly Parse(object value) => DateOnly.FromDateTime((DateTime)value);

    public override void SetValue(IDbDataParameter parameter, DateOnly value)
    {
        parameter.DbType = DbType.Date;
        parameter.Value = value;
    }
}

/// <summary>
/// Type handler for Currency value objects to ensure proper mapping between database and domain models.
/// </summary>
internal sealed class CurrencyTypeHandler : SqlMapper.TypeHandler<Currency>
{
    public override Currency Parse(object value)
    {
        if (value is null or DBNull)
            return Currency.FromCode("");

        return Currency.FromCode(value.ToString()!);
    }

    public override void SetValue(IDbDataParameter parameter, Currency value)
    {
        parameter.DbType = DbType.String;
        parameter.Value = value?.Code ?? (object)DBNull.Value;
    }
}

/// <summary>
/// Type handler for Money value objects to handle the amount portion.
/// Note: Currency should be handled separately in queries.
/// </summary>
internal sealed class MoneyAmountTypeHandler : SqlMapper.TypeHandler<decimal>
{
    public override decimal Parse(object value)
    {
        if (value is null or DBNull)
            return 0m;

        return Convert.ToDecimal(value);
    }

    public override void SetValue(IDbDataParameter parameter, decimal value)
    {
        parameter.DbType = DbType.Decimal;
        parameter.Value = value;
    }
}
