using Dapper;
using System.Data;
using System.Text;

namespace Bookify.Infrastructure.Data.Extensions;

/// <summary>
/// Extension methods for <PERSON><PERSON> to provide additional functionality and utilities.
/// </summary>
public static class DapperExtensions
{
    /// <summary>
    /// Executes a bulk insert operation using Dapper.
    /// </summary>
    public static async Task<int> BulkInsertAsync<T>(
        this IDbConnection connection,
        string tableName,
        IEnumerable<T> entities,
        IDbTransaction? transaction = null,
        int? commandTimeout = null)
    {
        if (!entities.Any())
            return 0;

        var properties = typeof(T).GetProperties()
            .Where(p => p.CanRead && p.GetGetMethod() != null)
            .ToList();

        var columnNames = string.Join(", ", properties.Select(p => p.Name.ToSnakeCase()));
        var parameterNames = string.Join(", ", properties.Select(p => $"@{p.Name}"));

        var sql = $"INSERT INTO {tableName} ({columnNames}) VALUES ({parameterNames})";

        return await connection.ExecuteAsync(sql, entities, transaction, commandTimeout);
    }

    /// <summary>
    /// Executes a bulk update operation using Dapper.
    /// </summary>
    public static async Task<int> BulkUpdateAsync<T>(
        this IDbConnection connection,
        string tableName,
        IEnumerable<T> entities,
        string keyColumn,
        IDbTransaction? transaction = null,
        int? commandTimeout = null)
    {
        if (!entities.Any())
            return 0;

        var properties = typeof(T).GetProperties()
            .Where(p => p.CanRead && p.GetGetMethod() != null && 
                       !string.Equals(p.Name, keyColumn, StringComparison.OrdinalIgnoreCase))
            .ToList();

        var setClause = string.Join(", ", properties.Select(p => $"{p.Name.ToSnakeCase()} = @{p.Name}"));
        var sql = $"UPDATE {tableName} SET {setClause} WHERE {keyColumn.ToSnakeCase()} = @{keyColumn}";

        return await connection.ExecuteAsync(sql, entities, transaction, commandTimeout);
    }

    /// <summary>
    /// Executes a bulk delete operation using Dapper.
    /// </summary>
    public static async Task<int> BulkDeleteAsync<T>(
        this IDbConnection connection,
        string tableName,
        string keyColumn,
        IEnumerable<T> keyValues,
        IDbTransaction? transaction = null,
        int? commandTimeout = null)
    {
        if (!keyValues.Any())
            return 0;

        var sql = $"DELETE FROM {tableName} WHERE {keyColumn.ToSnakeCase()} = ANY(@KeyValues)";
        return await connection.ExecuteAsync(sql, new { KeyValues = keyValues.ToArray() }, transaction, commandTimeout);
    }

    /// <summary>
    /// Executes a query with retry logic for transient failures.
    /// </summary>
    public static async Task<T?> QueryWithRetryAsync<T>(
        this IDbConnection connection,
        string sql,
        object? parameters = null,
        int maxRetries = 3,
        TimeSpan? retryDelay = null,
        IDbTransaction? transaction = null,
        int? commandTimeout = null)
    {
        var delay = retryDelay ?? TimeSpan.FromMilliseconds(100);
        var attempt = 0;

        while (attempt <= maxRetries)
        {
            try
            {
                return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters, transaction, commandTimeout);
            }
            catch (Exception ex) when (IsTransientError(ex) && attempt < maxRetries)
            {
                attempt++;
                await Task.Delay(delay * attempt);
            }
        }

        // Final attempt without catching exceptions
        return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters, transaction, commandTimeout);
    }

    /// <summary>
    /// Executes multiple queries in a single connection with proper disposal.
    /// </summary>
    public static async Task<TResult> QueryMultipleAsync<TResult>(
        this IDbConnection connection,
        string sql,
        Func<SqlMapper.GridReader, Task<TResult>> readerFunc,
        object? parameters = null,
        IDbTransaction? transaction = null,
        int? commandTimeout = null)
    {
        using var reader = await connection.QueryMultipleAsync(sql, parameters, transaction, commandTimeout);
        return await readerFunc(reader);
    }

    /// <summary>
    /// Converts a string to snake_case for database column naming.
    /// </summary>
    public static string ToSnakeCase(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var result = new StringBuilder();
        for (int i = 0; i < input.Length; i++)
        {
            var currentChar = input[i];
            if (char.IsUpper(currentChar) && i > 0)
            {
                result.Append('_');
            }
            result.Append(char.ToLower(currentChar));
        }

        return result.ToString();
    }

    /// <summary>
    /// Determines if an exception is a transient error that can be retried.
    /// </summary>
    private static bool IsTransientError(Exception exception)
    {
        // Add specific database provider transient error detection logic here
        // This is a simplified version - in production, you'd want to check for specific error codes
        return exception.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) ||
               exception.Message.Contains("connection", StringComparison.OrdinalIgnoreCase) ||
               exception.Message.Contains("network", StringComparison.OrdinalIgnoreCase);
    }
}

/// <summary>
/// Dynamic query builder for constructing SQL queries at runtime.
/// </summary>
public class DynamicQueryBuilder
{
    private readonly StringBuilder _selectClause = new();
    private readonly StringBuilder _fromClause = new();
    private readonly StringBuilder _joinClause = new();
    private readonly StringBuilder _whereClause = new();
    private readonly StringBuilder _orderByClause = new();
    private readonly StringBuilder _groupByClause = new();
    private readonly StringBuilder _havingClause = new();
    private string? _limitClause;
    private readonly DynamicParameters _parameters = new();

    public DynamicQueryBuilder Select(string columns)
    {
        if (_selectClause.Length > 0)
            _selectClause.Append(", ");
        _selectClause.Append(columns);
        return this;
    }

    public DynamicQueryBuilder From(string table)
    {
        _fromClause.Append(table);
        return this;
    }

    public DynamicQueryBuilder Join(string joinClause)
    {
        if (_joinClause.Length > 0)
            _joinClause.Append(" ");
        _joinClause.Append(joinClause);
        return this;
    }

    public DynamicQueryBuilder Where(string condition, object? value = null)
    {
        if (_whereClause.Length > 0)
            _whereClause.Append(" AND ");
        
        _whereClause.Append(condition);
        
        if (value != null)
        {
            var paramName = $"param{_parameters.ParameterNames.Count()}";
            _whereClause.Replace("?", $"@{paramName}");
            _parameters.Add(paramName, value);
        }
        
        return this;
    }

    public DynamicQueryBuilder WhereIf(bool condition, string whereClause, object? value = null)
    {
        if (condition)
            Where(whereClause, value);
        return this;
    }

    public DynamicQueryBuilder OrderBy(string orderBy)
    {
        if (_orderByClause.Length > 0)
            _orderByClause.Append(", ");
        _orderByClause.Append(orderBy);
        return this;
    }

    public DynamicQueryBuilder GroupBy(string groupBy)
    {
        if (_groupByClause.Length > 0)
            _groupByClause.Append(", ");
        _groupByClause.Append(groupBy);
        return this;
    }

    public DynamicQueryBuilder Having(string having)
    {
        if (_havingClause.Length > 0)
            _havingClause.Append(" AND ");
        _havingClause.Append(having);
        return this;
    }

    public DynamicQueryBuilder Limit(int limit, int? offset = null)
    {
        _limitClause = offset.HasValue ? $"LIMIT {limit} OFFSET {offset}" : $"LIMIT {limit}";
        return this;
    }

    public DynamicQueryBuilder AddParameter(string name, object value)
    {
        _parameters.Add(name, value);
        return this;
    }

    public (string sql, DynamicParameters parameters) Build()
    {
        var sql = new StringBuilder();
        
        sql.Append("SELECT ");
        sql.Append(_selectClause.Length > 0 ? _selectClause.ToString() : "*");
        
        if (_fromClause.Length > 0)
        {
            sql.Append(" FROM ");
            sql.Append(_fromClause);
        }

        if (_joinClause.Length > 0)
        {
            sql.Append(" ");
            sql.Append(_joinClause);
        }

        if (_whereClause.Length > 0)
        {
            sql.Append(" WHERE ");
            sql.Append(_whereClause);
        }

        if (_groupByClause.Length > 0)
        {
            sql.Append(" GROUP BY ");
            sql.Append(_groupByClause);
        }

        if (_havingClause.Length > 0)
        {
            sql.Append(" HAVING ");
            sql.Append(_havingClause);
        }

        if (_orderByClause.Length > 0)
        {
            sql.Append(" ORDER BY ");
            sql.Append(_orderByClause);
        }

        if (!string.IsNullOrEmpty(_limitClause))
        {
            sql.Append(" ");
            sql.Append(_limitClause);
        }

        return (sql.ToString(), _parameters);
    }
}

/// <summary>
/// Utility class for common database operations.
/// </summary>
public static class DatabaseUtilities
{
    /// <summary>
    /// Executes a database operation within a transaction.
    /// </summary>
    public static async Task<T> ExecuteInTransactionAsync<T>(
        IDbConnection connection,
        Func<IDbConnection, IDbTransaction, Task<T>> operation,
        IsolationLevel isolationLevel = IsolationLevel.ReadCommitted)
    {
        if (connection.State != ConnectionState.Open)
            connection.Open();

        using var transaction = connection.BeginTransaction(isolationLevel);
        try
        {
            var result = await operation(connection, transaction);
            transaction.Commit();
            return result;
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }

    /// <summary>
    /// Executes a database operation within a transaction (void return).
    /// </summary>
    public static async Task ExecuteInTransactionAsync(
        IDbConnection connection,
        Func<IDbConnection, IDbTransaction, Task> operation,
        IsolationLevel isolationLevel = IsolationLevel.ReadCommitted)
    {
        if (connection.State != ConnectionState.Open)
            connection.Open();

        using var transaction = connection.BeginTransaction(isolationLevel);
        try
        {
            await operation(connection, transaction);
            transaction.Commit();
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }

    /// <summary>
    /// Checks if a table exists in the database.
    /// </summary>
    public static async Task<bool> TableExistsAsync(IDbConnection connection, string tableName, string? schema = null)
    {
        var sql = schema != null
            ? "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = @Schema AND table_name = @TableName"
            : "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = @TableName";

        var count = await connection.ExecuteScalarAsync<int>(sql, new { Schema = schema, TableName = tableName });
        return count > 0;
    }

    /// <summary>
    /// Gets the column names for a table.
    /// </summary>
    public static async Task<IEnumerable<string>> GetTableColumnsAsync(IDbConnection connection, string tableName, string? schema = null)
    {
        var sql = schema != null
            ? "SELECT column_name FROM information_schema.columns WHERE table_schema = @Schema AND table_name = @TableName ORDER BY ordinal_position"
            : "SELECT column_name FROM information_schema.columns WHERE table_name = @TableName ORDER BY ordinal_position";

        return await connection.QueryAsync<string>(sql, new { Schema = schema, TableName = tableName });
    }
}
