using Bookify.Application.Abstractions.Data;
using Bookify.Domain.Entities.Bookings.Enums;
using Bookify.Domain.Shared;
using Dapper;

namespace Bookify.Infrastructure.Data.QueryServices;

/// <summary>
/// Specialized query service for booking-related read operations using Dapper.
/// Provides optimized queries for complex booking scenarios.
/// </summary>
public interface IBookingQueryService
{
    Task<BookingDetailsDto?> GetBookingDetailsAsync(Guid bookingId, CancellationToken cancellationToken = default);
    Task<IEnumerable<BookingSummaryDto>> GetUserBookingsAsync(Guid userId, BookingStatus? status = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<BookingSummaryDto>> GetApartmentBookingsAsync(Guid apartmentId, DateOnly? fromDate = null, DateOnly? toDate = null, CancellationToken cancellationToken = default);
    Task<BookingStatisticsDto> GetBookingStatisticsAsync(Guid? apartmentId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<BookingConflictDto>> GetBookingConflictsAsync(Guid apartmentId, DateOnly startDate, DateOnly endDate, CancellationToken cancellationToken = default);
    Task<decimal> GetTotalRevenueAsync(Guid? apartmentId = null, DateOnly? fromDate = null, DateOnly? toDate = null, CancellationToken cancellationToken = default);
}

internal sealed class BookingQueryService : DapperRepository, IBookingQueryService
{
    public BookingQueryService(ISqlConnectionFactory connectionFactory) : base(connectionFactory)
    {
    }

    public async Task<BookingDetailsDto?> GetBookingDetailsAsync(Guid bookingId, CancellationToken cancellationToken = default)
    {
        const string sql = """
            SELECT 
                b.id AS BookingId,
                b.status AS Status,
                b.duration_start AS StartDate,
                b.duration_end AS EndDate,
                b.price_for_period_amount AS PriceAmount,
                b.price_for_period_currency AS PriceCurrency,
                b.cleaning_fee_amount AS CleaningFeeAmount,
                b.cleaning_fee_currency AS CleaningFeeCurrency,
                b.amenities_up_charge_amount AS AmenitiesUpChargeAmount,
                b.amenities_up_charge_currency AS AmenitiesUpChargeCurrency,
                b.total_price_amount AS TotalPriceAmount,
                b.total_price_currency AS TotalPriceCurrency,
                b.created_on_utc AS CreatedOnUtc,
                b.confirmed_on_utc AS ConfirmedOnUtc,
                b.rejected_on_utc AS RejectedOnUtc,
                b.completed_on_utc AS CompletedOnUtc,
                b.cancelled_on_utc AS CancelledOnUtc,
                -- User details
                u.id AS UserId,
                u.first_name AS UserFirstName,
                u.last_name AS UserLastName,
                u.email AS UserEmail,
                -- Apartment details
                a.id AS ApartmentId,
                a.name AS ApartmentName,
                a.description AS ApartmentDescription,
                a.address_country AS Country,
                a.address_state AS State,
                a.address_city AS City,
                a.address_street AS Street,
                a.address_zip_code AS ZipCode
            FROM bookings b
            INNER JOIN users u ON b.user_id = u.id
            INNER JOIN apartments a ON b.apartment_id = a.id
            WHERE b.id = @BookingId
            """;

        var result = await QueryAsync<BookingDetailsDto, UserDto, ApartmentDto, BookingDetailsDto>(
            sql,
            (booking, user, apartment) =>
            {
                booking.User = user;
                booking.Apartment = apartment;
                return booking;
            },
            new { BookingId = bookingId },
            splitOn: "UserId,ApartmentId",
            cancellationToken);

        return result.FirstOrDefault();
    }

    public async Task<IEnumerable<BookingSummaryDto>> GetUserBookingsAsync(Guid userId, BookingStatus? status = null, CancellationToken cancellationToken = default)
    {
        var sql = """
            SELECT 
                b.id AS BookingId,
                b.status AS Status,
                b.duration_start AS StartDate,
                b.duration_end AS EndDate,
                b.total_price_amount AS TotalAmount,
                b.total_price_currency AS Currency,
                b.created_on_utc AS CreatedOnUtc,
                a.name AS ApartmentName,
                a.address_city AS City,
                a.address_country AS Country
            FROM bookings b
            INNER JOIN apartments a ON b.apartment_id = a.id
            WHERE b.user_id = @UserId
            """;

        var parameters = new DynamicParameters();
        parameters.Add("UserId", userId);

        if (status.HasValue)
        {
            sql += " AND b.status = @Status";
            parameters.Add("Status", (int)status.Value);
        }

        sql += " ORDER BY b.created_on_utc DESC";

        return await QueryAsync<BookingSummaryDto>(sql, parameters, cancellationToken);
    }

    public async Task<IEnumerable<BookingSummaryDto>> GetApartmentBookingsAsync(Guid apartmentId, DateOnly? fromDate = null, DateOnly? toDate = null, CancellationToken cancellationToken = default)
    {
        var sql = """
            SELECT 
                b.id AS BookingId,
                b.status AS Status,
                b.duration_start AS StartDate,
                b.duration_end AS EndDate,
                b.total_price_amount AS TotalAmount,
                b.total_price_currency AS Currency,
                b.created_on_utc AS CreatedOnUtc,
                u.first_name AS UserFirstName,
                u.last_name AS UserLastName,
                u.email AS UserEmail
            FROM bookings b
            INNER JOIN users u ON b.user_id = u.id
            WHERE b.apartment_id = @ApartmentId
            """;

        var parameters = new DynamicParameters();
        parameters.Add("ApartmentId", apartmentId);

        if (fromDate.HasValue)
        {
            sql += " AND b.duration_start >= @FromDate";
            parameters.Add("FromDate", fromDate.Value);
        }

        if (toDate.HasValue)
        {
            sql += " AND b.duration_end <= @ToDate";
            parameters.Add("ToDate", toDate.Value);
        }

        sql += " ORDER BY b.duration_start ASC";

        return await QueryAsync<BookingSummaryDto>(sql, parameters, cancellationToken);
    }

    public async Task<BookingStatisticsDto> GetBookingStatisticsAsync(Guid? apartmentId = null, CancellationToken cancellationToken = default)
    {
        var sql = """
            SELECT 
                COUNT(*) AS TotalBookings,
                COUNT(CASE WHEN status = @ReservedStatus THEN 1 END) AS ReservedCount,
                COUNT(CASE WHEN status = @ConfirmedStatus THEN 1 END) AS ConfirmedCount,
                COUNT(CASE WHEN status = @CompletedStatus THEN 1 END) AS CompletedCount,
                COUNT(CASE WHEN status = @CancelledStatus THEN 1 END) AS CancelledCount,
                COUNT(CASE WHEN status = @RejectedStatus THEN 1 END) AS RejectedCount,
                COALESCE(SUM(CASE WHEN status = @CompletedStatus THEN total_price_amount ELSE 0 END), 0) AS TotalRevenue,
                COALESCE(AVG(CASE WHEN status = @CompletedStatus THEN total_price_amount END), 0) AS AverageBookingValue
            FROM bookings
            """;

        var parameters = new DynamicParameters();
        parameters.Add("ReservedStatus", (int)BookingStatus.Reserved);
        parameters.Add("ConfirmedStatus", (int)BookingStatus.Confirmed);
        parameters.Add("CompletedStatus", (int)BookingStatus.Completed);
        parameters.Add("CancelledStatus", (int)BookingStatus.Cancelled);
        parameters.Add("RejectedStatus", (int)BookingStatus.Rejected);

        if (apartmentId.HasValue)
        {
            sql += " WHERE apartment_id = @ApartmentId";
            parameters.Add("ApartmentId", apartmentId.Value);
        }

        return await QuerySingleOrDefaultAsync<BookingStatisticsDto>(sql, parameters, cancellationToken) 
               ?? new BookingStatisticsDto();
    }

    public async Task<IEnumerable<BookingConflictDto>> GetBookingConflictsAsync(Guid apartmentId, DateOnly startDate, DateOnly endDate, CancellationToken cancellationToken = default)
    {
        const string sql = """
            SELECT 
                b.id AS BookingId,
                b.duration_start AS StartDate,
                b.duration_end AS EndDate,
                b.status AS Status,
                u.first_name AS UserFirstName,
                u.last_name AS UserLastName,
                u.email AS UserEmail
            FROM bookings b
            INNER JOIN users u ON b.user_id = u.id
            WHERE b.apartment_id = @ApartmentId
              AND b.status IN (@ReservedStatus, @ConfirmedStatus, @CompletedStatus)
              AND (
                  (b.duration_start <= @EndDate AND b.duration_end >= @StartDate)
              )
            ORDER BY b.duration_start ASC
            """;

        var parameters = new DynamicParameters();
        parameters.Add("ApartmentId", apartmentId);
        parameters.Add("StartDate", startDate);
        parameters.Add("EndDate", endDate);
        parameters.Add("ReservedStatus", (int)BookingStatus.Reserved);
        parameters.Add("ConfirmedStatus", (int)BookingStatus.Confirmed);
        parameters.Add("CompletedStatus", (int)BookingStatus.Completed);

        return await QueryAsync<BookingConflictDto>(sql, parameters, cancellationToken);
    }

    public async Task<decimal> GetTotalRevenueAsync(Guid? apartmentId = null, DateOnly? fromDate = null, DateOnly? toDate = null, CancellationToken cancellationToken = default)
    {
        var sql = """
            SELECT COALESCE(SUM(total_price_amount), 0)
            FROM bookings
            WHERE status = @CompletedStatus
            """;

        var parameters = new DynamicParameters();
        parameters.Add("CompletedStatus", (int)BookingStatus.Completed);

        if (apartmentId.HasValue)
        {
            sql += " AND apartment_id = @ApartmentId";
            parameters.Add("ApartmentId", apartmentId.Value);
        }

        if (fromDate.HasValue)
        {
            sql += " AND duration_start >= @FromDate";
            parameters.Add("FromDate", fromDate.Value);
        }

        if (toDate.HasValue)
        {
            sql += " AND duration_end <= @ToDate";
            parameters.Add("ToDate", toDate.Value);
        }

        return await ExecuteScalarAsync<decimal>(sql, parameters, cancellationToken);
    }
}

// DTOs for query results
public class BookingDetailsDto
{
    public Guid BookingId { get; set; }
    public BookingStatus Status { get; set; }
    public DateOnly StartDate { get; set; }
    public DateOnly EndDate { get; set; }
    public decimal PriceAmount { get; set; }
    public string PriceCurrency { get; set; } = string.Empty;
    public decimal CleaningFeeAmount { get; set; }
    public string CleaningFeeCurrency { get; set; } = string.Empty;
    public decimal AmenitiesUpChargeAmount { get; set; }
    public string AmenitiesUpChargeCurrency { get; set; } = string.Empty;
    public decimal TotalPriceAmount { get; set; }
    public string TotalPriceCurrency { get; set; } = string.Empty;
    public DateTime CreatedOnUtc { get; set; }
    public DateTime? ConfirmedOnUtc { get; set; }
    public DateTime? RejectedOnUtc { get; set; }
    public DateTime? CompletedOnUtc { get; set; }
    public DateTime? CancelledOnUtc { get; set; }
    public UserDto User { get; set; } = new();
    public ApartmentDto Apartment { get; set; } = new();
}

public class BookingSummaryDto
{
    public Guid BookingId { get; set; }
    public BookingStatus Status { get; set; }
    public DateOnly StartDate { get; set; }
    public DateOnly EndDate { get; set; }
    public decimal TotalAmount { get; set; }
    public string Currency { get; set; } = string.Empty;
    public DateTime CreatedOnUtc { get; set; }
    public string ApartmentName { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string UserFirstName { get; set; } = string.Empty;
    public string UserLastName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
}

public class BookingStatisticsDto
{
    public int TotalBookings { get; set; }
    public int ReservedCount { get; set; }
    public int ConfirmedCount { get; set; }
    public int CompletedCount { get; set; }
    public int CancelledCount { get; set; }
    public int RejectedCount { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageBookingValue { get; set; }
}

public class BookingConflictDto
{
    public Guid BookingId { get; set; }
    public DateOnly StartDate { get; set; }
    public DateOnly EndDate { get; set; }
    public BookingStatus Status { get; set; }
    public string UserFirstName { get; set; } = string.Empty;
    public string UserLastName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
}

public class UserDto
{
    public Guid UserId { get; set; }
    public string UserFirstName { get; set; } = string.Empty;
    public string UserLastName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
}

public class ApartmentDto
{
    public Guid ApartmentId { get; set; }
    public string ApartmentName { get; set; } = string.Empty;
    public string ApartmentDescription { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Street { get; set; } = string.Empty;
    public string ZipCode { get; set; } = string.Empty;
}
