using Bookify.Application.Abstractions.Data;
using Bookify.Domain.Entities.Apartments.Enums;
using Bookify.Domain.Entities.Bookings.Enums;
using Dapper;

namespace Bookify.Infrastructure.Data.QueryServices;

/// <summary>
/// Specialized query service for apartment-related read operations using Dapper.
/// Provides optimized queries for apartment search, availability, and analytics.
/// </summary>
public interface IApartmentQueryService
{
    Task<ApartmentDetailsDto?> GetApartmentDetailsAsync(Guid apartmentId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ApartmentSummaryDto>> SearchApartmentsAsync(ApartmentSearchCriteria criteria, CancellationToken cancellationToken = default);
    Task<IEnumerable<ApartmentAvailabilityDto>> GetApartmentAvailabilityAsync(Guid apartmentId, DateOnly fromDate, DateOnly toDate, CancellationToken cancellationToken = default);
    Task<ApartmentAnalyticsDto> GetApartmentAnalyticsAsync(Guid apartmentId, CancellationToken cancellationToken = default);
    Task<IEnumerable<ApartmentSummaryDto>> GetTopPerformingApartmentsAsync(int count = 10, CancellationToken cancellationToken = default);
    Task<IEnumerable<ApartmentSummaryDto>> GetApartmentsByLocationAsync(string? country = null, string? state = null, string? city = null, CancellationToken cancellationToken = default);
}

internal sealed class ApartmentQueryService : DapperRepository, IApartmentQueryService
{
    private static readonly int[] ActiveBookingStatuses =
    {
        (int)BookingStatus.Reserved,
        (int)BookingStatus.Confirmed,
        (int)BookingStatus.Completed
    };

    public ApartmentQueryService(ISqlConnectionFactory connectionFactory) : base(connectionFactory)
    {
    }

    public async Task<ApartmentDetailsDto?> GetApartmentDetailsAsync(Guid apartmentId, CancellationToken cancellationToken = default)
    {
        const string sql = """
            SELECT 
                a.id AS ApartmentId,
                a.name AS Name,
                a.description AS Description,
                a.address_country AS Country,
                a.address_state AS State,
                a.address_city AS City,
                a.address_street AS Street,
                a.address_zip_code AS ZipCode,
                a.price_amount AS PriceAmount,
                a.price_currency AS PriceCurrency,
                a.cleaning_fee_amount AS CleaningFeeAmount,
                a.cleaning_fee_currency AS CleaningFeeCurrency,
                a.amenities AS Amenities,
                a.last_booked_on_utc AS LastBookedOnUtc,
                -- Booking statistics
                COUNT(b.id) AS TotalBookings,
                COUNT(CASE WHEN b.status = @CompletedStatus THEN 1 END) AS CompletedBookings,
                COALESCE(SUM(CASE WHEN b.status = @CompletedStatus THEN b.total_price_amount ELSE 0 END), 0) AS TotalRevenue,
                COALESCE(AVG(CASE WHEN b.status = @CompletedStatus THEN b.total_price_amount END), 0) AS AverageBookingValue
            FROM apartments a
            LEFT JOIN bookings b ON a.id = b.apartment_id
            WHERE a.id = @ApartmentId
            GROUP BY a.id, a.name, a.description, a.address_country, a.address_state, 
                     a.address_city, a.address_street, a.address_zip_code, a.price_amount, 
                     a.price_currency, a.cleaning_fee_amount, a.cleaning_fee_currency, 
                     a.amenities, a.last_booked_on_utc
            """;

        var parameters = new DynamicParameters();
        parameters.Add("ApartmentId", apartmentId);
        parameters.Add("CompletedStatus", (int)BookingStatus.Completed);

        return await QueryFirstOrDefaultAsync<ApartmentDetailsDto>(sql, parameters, cancellationToken);
    }

    public async Task<IEnumerable<ApartmentSummaryDto>> SearchApartmentsAsync(ApartmentSearchCriteria criteria, CancellationToken cancellationToken = default)
    {
        var sql = """
            SELECT DISTINCT
                a.id AS ApartmentId,
                a.name AS Name,
                a.description AS Description,
                a.address_country AS Country,
                a.address_state AS State,
                a.address_city AS City,
                a.address_street AS Street,
                a.address_zip_code AS ZipCode,
                a.price_amount AS PriceAmount,
                a.price_currency AS PriceCurrency,
                a.cleaning_fee_amount AS CleaningFeeAmount,
                a.cleaning_fee_currency AS CleaningFeeCurrency,
                a.amenities AS Amenities
            FROM apartments a
            WHERE 1=1
            """;

        var parameters = new DynamicParameters();

        // Location filters
        if (!string.IsNullOrWhiteSpace(criteria.Country))
        {
            sql += " AND LOWER(a.address_country) LIKE LOWER(@Country)";
            parameters.Add("Country", $"%{criteria.Country}%");
        }

        if (!string.IsNullOrWhiteSpace(criteria.State))
        {
            sql += " AND LOWER(a.address_state) LIKE LOWER(@State)";
            parameters.Add("State", $"%{criteria.State}%");
        }

        if (!string.IsNullOrWhiteSpace(criteria.City))
        {
            sql += " AND LOWER(a.address_city) LIKE LOWER(@City)";
            parameters.Add("City", $"%{criteria.City}%");
        }

        // Price range filters
        if (criteria.MinPrice.HasValue)
        {
            sql += " AND a.price_amount >= @MinPrice";
            parameters.Add("MinPrice", criteria.MinPrice.Value);
        }

        if (criteria.MaxPrice.HasValue)
        {
            sql += " AND a.price_amount <= @MaxPrice";
            parameters.Add("MaxPrice", criteria.MaxPrice.Value);
        }

        // Amenities filter
        if (criteria.RequiredAmenities?.Any() == true)
        {
            sql += " AND a.amenities @> @RequiredAmenities";
            parameters.Add("RequiredAmenities", criteria.RequiredAmenities.Cast<int>().ToArray());
        }

        // Availability filter
        if (criteria.StartDate.HasValue && criteria.EndDate.HasValue)
        {
            sql += """
                AND NOT EXISTS (
                    SELECT 1 FROM bookings b 
                    WHERE b.apartment_id = a.id 
                      AND b.status = ANY(@ActiveBookingStatuses)
                      AND b.duration_start <= @EndDate 
                      AND b.duration_end >= @StartDate
                )
                """;
            parameters.Add("StartDate", criteria.StartDate.Value);
            parameters.Add("EndDate", criteria.EndDate.Value);
            parameters.Add("ActiveBookingStatuses", ActiveBookingStatuses);
        }

        // Ordering
        sql += criteria.OrderBy switch
        {
            "price_asc" => " ORDER BY a.price_amount ASC",
            "price_desc" => " ORDER BY a.price_amount DESC",
            "name" => " ORDER BY a.name ASC",
            _ => " ORDER BY a.name ASC"
        };

        // Pagination
        if (criteria.PageSize.HasValue && criteria.PageNumber.HasValue)
        {
            var offset = (criteria.PageNumber.Value - 1) * criteria.PageSize.Value;
            sql += $" LIMIT {criteria.PageSize.Value} OFFSET {offset}";
        }

        return await QueryAsync<ApartmentSummaryDto>(sql, parameters, cancellationToken);
    }

    public async Task<IEnumerable<ApartmentAvailabilityDto>> GetApartmentAvailabilityAsync(Guid apartmentId, DateOnly fromDate, DateOnly toDate, CancellationToken cancellationToken = default)
    {
        const string sql = """
            WITH date_range AS (
                SELECT generate_series(@FromDate::date, @ToDate::date, '1 day'::interval)::date AS check_date
            ),
            bookings_in_range AS (
                SELECT 
                    b.duration_start,
                    b.duration_end,
                    b.status
                FROM bookings b
                WHERE b.apartment_id = @ApartmentId
                  AND b.status = ANY(@ActiveBookingStatuses)
                  AND b.duration_start <= @ToDate
                  AND b.duration_end >= @FromDate
            )
            SELECT 
                dr.check_date AS Date,
                CASE 
                    WHEN bir.duration_start IS NOT NULL THEN false
                    ELSE true
                END AS IsAvailable,
                bir.status AS BookingStatus
            FROM date_range dr
            LEFT JOIN bookings_in_range bir ON dr.check_date >= bir.duration_start AND dr.check_date <= bir.duration_end
            ORDER BY dr.check_date
            """;

        var parameters = new DynamicParameters();
        parameters.Add("ApartmentId", apartmentId);
        parameters.Add("FromDate", fromDate);
        parameters.Add("ToDate", toDate);
        parameters.Add("ActiveBookingStatuses", ActiveBookingStatuses);

        return await QueryAsync<ApartmentAvailabilityDto>(sql, parameters, cancellationToken);
    }

    public async Task<ApartmentAnalyticsDto> GetApartmentAnalyticsAsync(Guid apartmentId, CancellationToken cancellationToken = default)
    {
        const string sql = """
            SELECT 
                COUNT(b.id) AS TotalBookings,
                COUNT(CASE WHEN b.status = @ReservedStatus THEN 1 END) AS ReservedBookings,
                COUNT(CASE WHEN b.status = @ConfirmedStatus THEN 1 END) AS ConfirmedBookings,
                COUNT(CASE WHEN b.status = @CompletedStatus THEN 1 END) AS CompletedBookings,
                COUNT(CASE WHEN b.status = @CancelledStatus THEN 1 END) AS CancelledBookings,
                COUNT(CASE WHEN b.status = @RejectedStatus THEN 1 END) AS RejectedBookings,
                COALESCE(SUM(CASE WHEN b.status = @CompletedStatus THEN b.total_price_amount ELSE 0 END), 0) AS TotalRevenue,
                COALESCE(AVG(CASE WHEN b.status = @CompletedStatus THEN b.total_price_amount END), 0) AS AverageBookingValue,
                COALESCE(SUM(CASE WHEN b.status = @CompletedStatus THEN b.duration_end - b.duration_start + 1 ELSE 0 END), 0) AS TotalNightsBooked,
                CASE 
                    WHEN COUNT(CASE WHEN b.status = @CompletedStatus THEN 1 END) > 0 
                    THEN COALESCE(AVG(CASE WHEN b.status = @CompletedStatus THEN b.duration_end - b.duration_start + 1 END), 0)
                    ELSE 0 
                END AS AverageStayLength
            FROM apartments a
            LEFT JOIN bookings b ON a.id = b.apartment_id
            WHERE a.id = @ApartmentId
            GROUP BY a.id
            """;

        var parameters = new DynamicParameters();
        parameters.Add("ApartmentId", apartmentId);
        parameters.Add("ReservedStatus", (int)BookingStatus.Reserved);
        parameters.Add("ConfirmedStatus", (int)BookingStatus.Confirmed);
        parameters.Add("CompletedStatus", (int)BookingStatus.Completed);
        parameters.Add("CancelledStatus", (int)BookingStatus.Cancelled);
        parameters.Add("RejectedStatus", (int)BookingStatus.Rejected);

        return await QueryFirstOrDefaultAsync<ApartmentAnalyticsDto>(sql, parameters, cancellationToken) 
               ?? new ApartmentAnalyticsDto();
    }

    public async Task<IEnumerable<ApartmentSummaryDto>> GetTopPerformingApartmentsAsync(int count = 10, CancellationToken cancellationToken = default)
    {
        const string sql = """
            SELECT 
                a.id AS ApartmentId,
                a.name AS Name,
                a.description AS Description,
                a.address_country AS Country,
                a.address_state AS State,
                a.address_city AS City,
                a.address_street AS Street,
                a.address_zip_code AS ZipCode,
                a.price_amount AS PriceAmount,
                a.price_currency AS PriceCurrency,
                a.cleaning_fee_amount AS CleaningFeeAmount,
                a.cleaning_fee_currency AS CleaningFeeCurrency,
                a.amenities AS Amenities,
                COALESCE(SUM(CASE WHEN b.status = @CompletedStatus THEN b.total_price_amount ELSE 0 END), 0) AS TotalRevenue,
                COUNT(CASE WHEN b.status = @CompletedStatus THEN 1 END) AS CompletedBookings
            FROM apartments a
            LEFT JOIN bookings b ON a.id = b.apartment_id
            GROUP BY a.id, a.name, a.description, a.address_country, a.address_state, 
                     a.address_city, a.address_street, a.address_zip_code, a.price_amount, 
                     a.price_currency, a.cleaning_fee_amount, a.cleaning_fee_currency, a.amenities
            ORDER BY TotalRevenue DESC, CompletedBookings DESC
            LIMIT @Count
            """;

        var parameters = new DynamicParameters();
        parameters.Add("CompletedStatus", (int)BookingStatus.Completed);
        parameters.Add("Count", count);

        return await QueryAsync<ApartmentSummaryDto>(sql, parameters, cancellationToken);
    }

    public async Task<IEnumerable<ApartmentSummaryDto>> GetApartmentsByLocationAsync(string? country = null, string? state = null, string? city = null, CancellationToken cancellationToken = default)
    {
        var sql = """
            SELECT 
                a.id AS ApartmentId,
                a.name AS Name,
                a.description AS Description,
                a.address_country AS Country,
                a.address_state AS State,
                a.address_city AS City,
                a.address_street AS Street,
                a.address_zip_code AS ZipCode,
                a.price_amount AS PriceAmount,
                a.price_currency AS PriceCurrency,
                a.cleaning_fee_amount AS CleaningFeeAmount,
                a.cleaning_fee_currency AS CleaningFeeCurrency,
                a.amenities AS Amenities
            FROM apartments a
            WHERE 1=1
            """;

        var parameters = new DynamicParameters();

        if (!string.IsNullOrWhiteSpace(country))
        {
            sql += " AND LOWER(a.address_country) = LOWER(@Country)";
            parameters.Add("Country", country);
        }

        if (!string.IsNullOrWhiteSpace(state))
        {
            sql += " AND LOWER(a.address_state) = LOWER(@State)";
            parameters.Add("State", state);
        }

        if (!string.IsNullOrWhiteSpace(city))
        {
            sql += " AND LOWER(a.address_city) = LOWER(@City)";
            parameters.Add("City", city);
        }

        sql += " ORDER BY a.name ASC";

        return await QueryAsync<ApartmentSummaryDto>(sql, parameters, cancellationToken);
    }
}

// Search criteria class
public class ApartmentSearchCriteria
{
    public string? Country { get; set; }
    public string? State { get; set; }
    public string? City { get; set; }
    public decimal? MinPrice { get; set; }
    public decimal? MaxPrice { get; set; }
    public List<Amenity>? RequiredAmenities { get; set; }
    public DateOnly? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public string OrderBy { get; set; } = "name";
    public int? PageSize { get; set; }
    public int? PageNumber { get; set; }
}

// DTOs for query results
public class ApartmentDetailsDto
{
    public Guid ApartmentId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Street { get; set; } = string.Empty;
    public string ZipCode { get; set; } = string.Empty;
    public decimal PriceAmount { get; set; }
    public string PriceCurrency { get; set; } = string.Empty;
    public decimal CleaningFeeAmount { get; set; }
    public string CleaningFeeCurrency { get; set; } = string.Empty;
    public int[] Amenities { get; set; } = Array.Empty<int>();
    public DateTime? LastBookedOnUtc { get; set; }
    public int TotalBookings { get; set; }
    public int CompletedBookings { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageBookingValue { get; set; }
}

public class ApartmentSummaryDto
{
    public Guid ApartmentId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Country { get; set; } = string.Empty;
    public string State { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string Street { get; set; } = string.Empty;
    public string ZipCode { get; set; } = string.Empty;
    public decimal PriceAmount { get; set; }
    public string PriceCurrency { get; set; } = string.Empty;
    public decimal CleaningFeeAmount { get; set; }
    public string CleaningFeeCurrency { get; set; } = string.Empty;
    public int[] Amenities { get; set; } = Array.Empty<int>();
    public decimal TotalRevenue { get; set; }
    public int CompletedBookings { get; set; }
}

public class ApartmentAvailabilityDto
{
    public DateOnly Date { get; set; }
    public bool IsAvailable { get; set; }
    public BookingStatus? BookingStatus { get; set; }
}

public class ApartmentAnalyticsDto
{
    public int TotalBookings { get; set; }
    public int ReservedBookings { get; set; }
    public int ConfirmedBookings { get; set; }
    public int CompletedBookings { get; set; }
    public int CancelledBookings { get; set; }
    public int RejectedBookings { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal AverageBookingValue { get; set; }
    public int TotalNightsBooked { get; set; }
    public decimal AverageStayLength { get; set; }
}
