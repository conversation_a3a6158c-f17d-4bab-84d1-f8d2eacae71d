using Bookify.Application.Abstractions.Data;
using Dapper;
using System.Data;
using System.Text;

namespace Bookify.Infrastructure.Data;

/// <summary>
/// Base repository class for Dapper operations providing common CRUD and query functionality.
/// This complements the existing EF Core repositories for read-heavy operations.
/// </summary>
public abstract class DapperRepository
{
    protected readonly ISqlConnectionFactory _connectionFactory;

    protected DapperRepository(ISqlConnectionFactory connectionFactory)
    {
        _connectionFactory = connectionFactory;
    }

    /// <summary>
    /// Executes a query and returns a single result or default.
    /// </summary>
    protected async Task<T?> QuerySingleOrDefaultAsync<T>(
        string sql, 
        object? parameters = null, 
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QuerySingleOrDefaultAsync<T>(sql, parameters);
    }

    /// <summary>
    /// Executes a query and returns the first result or default.
    /// </summary>
    protected async Task<T?> QueryFirstOrDefaultAsync<T>(
        string sql, 
        object? parameters = null, 
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters);
    }

    /// <summary>
    /// Executes a query and returns multiple results.
    /// </summary>
    protected async Task<IEnumerable<T>> QueryAsync<T>(
        string sql, 
        object? parameters = null, 
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QueryAsync<T>(sql, parameters);
    }

    /// <summary>
    /// Executes a query with multiple result sets.
    /// </summary>
    protected async Task<IEnumerable<TReturn>> QueryAsync<TFirst, TSecond, TReturn>(
        string sql,
        Func<TFirst, TSecond, TReturn> map,
        object? parameters = null,
        string splitOn = "Id",
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QueryAsync(sql, map, parameters, splitOn: splitOn);
    }

    /// <summary>
    /// Executes a query with three result sets.
    /// </summary>
    protected async Task<IEnumerable<TReturn>> QueryAsync<TFirst, TSecond, TThird, TReturn>(
        string sql,
        Func<TFirst, TSecond, TThird, TReturn> map,
        object? parameters = null,
        string splitOn = "Id",
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.QueryAsync(sql, map, parameters, splitOn: splitOn);
    }

    /// <summary>
    /// Executes a command and returns the number of affected rows.
    /// </summary>
    protected async Task<int> ExecuteAsync(
        string sql, 
        object? parameters = null, 
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.ExecuteAsync(sql, parameters);
    }

    /// <summary>
    /// Executes a command and returns a scalar value.
    /// </summary>
    protected async Task<T> ExecuteScalarAsync<T>(
        string sql, 
        object? parameters = null, 
        CancellationToken cancellationToken = default)
    {
        using var connection = _connectionFactory.CreateConnection();
        return await connection.ExecuteScalarAsync<T>(sql, parameters);
    }

    /// <summary>
    /// Executes multiple queries in a single connection.
    /// </summary>
    protected async Task<SqlMapper.GridReader> QueryMultipleAsync(
        string sql, 
        object? parameters = null, 
        CancellationToken cancellationToken = default)
    {
        var connection = _connectionFactory.CreateConnection();
        return await connection.QueryMultipleAsync(sql, parameters);
    }

    /// <summary>
    /// Builds a dynamic WHERE clause from the provided conditions.
    /// </summary>
    protected static (string whereClause, DynamicParameters parameters) BuildWhereClause(
        Dictionary<string, object?> conditions)
    {
        if (!conditions.Any())
            return (string.Empty, new DynamicParameters());

        var whereBuilder = new StringBuilder();
        var parameters = new DynamicParameters();
        var conditionClauses = new List<string>();

        foreach (var condition in conditions.Where(c => c.Value != null))
        {
            var paramName = $"@{condition.Key}";
            conditionClauses.Add($"{condition.Key} = {paramName}");
            parameters.Add(paramName, condition.Value);
        }

        if (conditionClauses.Any())
        {
            whereBuilder.Append("WHERE ");
            whereBuilder.Append(string.Join(" AND ", conditionClauses));
        }

        return (whereBuilder.ToString(), parameters);
    }

    /// <summary>
    /// Builds a dynamic ORDER BY clause.
    /// </summary>
    protected static string BuildOrderByClause(string? orderBy, bool ascending = true)
    {
        if (string.IsNullOrWhiteSpace(orderBy))
            return string.Empty;

        return $"ORDER BY {orderBy} {(ascending ? "ASC" : "DESC")}";
    }

    /// <summary>
    /// Builds pagination clause (LIMIT/OFFSET).
    /// </summary>
    protected static string BuildPaginationClause(int? pageSize, int? pageNumber)
    {
        if (!pageSize.HasValue || !pageNumber.HasValue)
            return string.Empty;

        var offset = (pageNumber.Value - 1) * pageSize.Value;
        return $"LIMIT {pageSize.Value} OFFSET {offset}";
    }
}

/// <summary>
/// Generic base repository for entities with strongly-typed IDs.
/// </summary>
public abstract class DapperRepository<TEntity, TId> : DapperRepository
    where TEntity : class
    where TId : class
{
    protected readonly string _tableName;

    protected DapperRepository(ISqlConnectionFactory connectionFactory, string tableName) 
        : base(connectionFactory)
    {
        _tableName = tableName;
    }

    /// <summary>
    /// Gets an entity by its ID.
    /// </summary>
    public virtual async Task<TEntity?> GetByIdAsync(TId id, CancellationToken cancellationToken = default)
    {
        var sql = $"SELECT * FROM {_tableName} WHERE id = @Id";
        return await QueryFirstOrDefaultAsync<TEntity>(sql, new { Id = id }, cancellationToken);
    }

    /// <summary>
    /// Gets all entities with optional filtering, ordering, and pagination.
    /// </summary>
    public virtual async Task<IEnumerable<TEntity>> GetAllAsync(
        Dictionary<string, object?>? filters = null,
        string? orderBy = null,
        bool ascending = true,
        int? pageSize = null,
        int? pageNumber = null,
        CancellationToken cancellationToken = default)
    {
        var sqlBuilder = new StringBuilder($"SELECT * FROM {_tableName}");
        
        var parameters = new DynamicParameters();
        
        if (filters?.Any() == true)
        {
            var (whereClause, whereParams) = BuildWhereClause(filters);
            sqlBuilder.Append($" {whereClause}");
            parameters.AddDynamicParams(whereParams);
        }

        if (!string.IsNullOrWhiteSpace(orderBy))
        {
            sqlBuilder.Append($" {BuildOrderByClause(orderBy, ascending)}");
        }

        if (pageSize.HasValue && pageNumber.HasValue)
        {
            sqlBuilder.Append($" {BuildPaginationClause(pageSize, pageNumber)}");
        }

        return await QueryAsync<TEntity>(sqlBuilder.ToString(), parameters, cancellationToken);
    }

    /// <summary>
    /// Gets the count of entities with optional filtering.
    /// </summary>
    public virtual async Task<int> GetCountAsync(
        Dictionary<string, object?>? filters = null,
        CancellationToken cancellationToken = default)
    {
        var sqlBuilder = new StringBuilder($"SELECT COUNT(*) FROM {_tableName}");
        
        var parameters = new DynamicParameters();
        
        if (filters?.Any() == true)
        {
            var (whereClause, whereParams) = BuildWhereClause(filters);
            sqlBuilder.Append($" {whereClause}");
            parameters.AddDynamicParams(whereParams);
        }

        return await ExecuteScalarAsync<int>(sqlBuilder.ToString(), parameters, cancellationToken);
    }

    /// <summary>
    /// Checks if an entity exists by ID.
    /// </summary>
    public virtual async Task<bool> ExistsAsync(TId id, CancellationToken cancellationToken = default)
    {
        var sql = $"SELECT COUNT(1) FROM {_tableName} WHERE id = @Id";
        var count = await ExecuteScalarAsync<int>(sql, new { Id = id }, cancellationToken);
        return count > 0;
    }
}
