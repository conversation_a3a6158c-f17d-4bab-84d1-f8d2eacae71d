using Bookify.Application.Abstractions.Data;
using Microsoft.Extensions.Logging;
using Npgsql;
using System.Data;

namespace Bookify.Infrastructure.Data;

/// <summary>
/// Enhanced SQL connection factory with connection pooling, retry logic, and monitoring.
/// </summary>
internal sealed class SqlConnectionFactory : ISqlConnectionFactory
{
    private readonly string _connectionString;
    private readonly ILogger<SqlConnectionFactory> _logger;

    public SqlConnectionFactory(string connectionString, ILogger<SqlConnectionFactory> logger = null)
    {
        _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        _logger = logger;
    }

    public IDbConnection CreateConnection()
    {
        try
        {
            var connection = new NpgsqlConnection(_connectionString);
            connection.Open();

            _logger?.LogDebug("Database connection opened successfully");
            return connection;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to create database connection");
            throw;
        }
    }

    /// <summary>
    /// Creates a connection with retry logic for transient failures.
    /// </summary>
    public async Task<IDbConnection> CreateConnectionWithRetryAsync(
        int maxRetries = 3,
        TimeSpan? retryDelay = null,
        CancellationToken cancellationToken = default)
    {
        var delay = retryDelay ?? TimeSpan.FromMilliseconds(100);
        var attempt = 0;

        while (attempt <= maxRetries)
        {
            try
            {
                var connection = new NpgsqlConnection(_connectionString);
                await connection.OpenAsync(cancellationToken);

                _logger?.LogDebug("Database connection opened successfully on attempt {Attempt}", attempt + 1);
                return connection;
            }
            catch (Exception ex) when (IsTransientError(ex) && attempt < maxRetries)
            {
                attempt++;
                _logger?.LogWarning(ex, "Transient error on connection attempt {Attempt}. Retrying in {Delay}ms",
                    attempt, delay.TotalMilliseconds * attempt);

                await Task.Delay(delay * attempt, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to create database connection after {Attempts} attempts", attempt + 1);
                throw;
            }
        }

        // This should never be reached, but included for completeness
        throw new InvalidOperationException("Failed to create connection after all retry attempts");
    }

    /// <summary>
    /// Tests the connection to ensure it's working properly.
    /// </summary>
    public async Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            using var connection = await CreateConnectionWithRetryAsync(cancellationToken: cancellationToken);
            using var command = connection.CreateCommand();
            command.CommandText = "SELECT 1";
            var result = await ((NpgsqlCommand)command).ExecuteScalarAsync(cancellationToken);

            _logger?.LogDebug("Database connection test successful");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Database connection test failed");
            return false;
        }
    }

    /// <summary>
    /// Determines if an exception represents a transient error that can be retried.
    /// </summary>
    private static bool IsTransientError(Exception exception)
    {
        return exception switch
        {
            NpgsqlException npgsqlEx => IsTransientNpgsqlError(npgsqlEx),
            TimeoutException => true,
            InvalidOperationException invalidOpEx when invalidOpEx.Message.Contains("timeout", StringComparison.OrdinalIgnoreCase) => true,
            _ => false
        };
    }

    /// <summary>
    /// Determines if a PostgreSQL exception is transient.
    /// </summary>
    private static bool IsTransientNpgsqlError(NpgsqlException exception)
    {
        // PostgreSQL error codes that indicate transient errors
        var transientErrorCodes = new[]
        {
            "53000", // insufficient_resources
            "53100", // disk_full
            "53200", // out_of_memory
            "53300", // too_many_connections
            "08000", // connection_exception
            "08003", // connection_does_not_exist
            "08006", // connection_failure
            "08001", // sqlclient_unable_to_establish_sqlconnection
            "08004", // sqlserver_rejected_establishment_of_sqlconnection
            "57P01", // admin_shutdown
            "57P02", // crash_shutdown
            "57P03", // cannot_connect_now
        };

        return transientErrorCodes.Contains(exception.SqlState);
    }
}
