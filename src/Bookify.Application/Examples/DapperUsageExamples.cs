using Bookify.Application.Abstractions.Data;
using Bookify.Application.Abstractions.Messaging;
using Bookify.Domain.Entities.Abstractions;
using Bookify.Infrastructure.Data.Extensions;
using Bookify.Infrastructure.Data.Monitoring;
using Bookify.Infrastructure.Data.QueryServices;
using Dapper;

namespace Bookify.Application.Examples;

/// <summary>
/// Examples demonstrating how to use the enhanced Dapper integration in the Bookify application.
/// These examples show best practices for implementing queries, commands, and monitoring.
/// </summary>
public static class DapperUsageExamples
{
    /// <summary>
    /// Example 1: Using the BookingQueryService for complex booking queries
    /// </summary>
    public class GetBookingAnalyticsQuery : IQuery<BookingAnalyticsResponse>
    {
        public Guid? ApartmentId { get; init; }
        public DateOnly? FromDate { get; init; }
        public DateOnly? ToDate { get; init; }
    }

    public class BookingAnalyticsResponse
    {
        public BookingStatisticsDto Statistics { get; init; } = new();
        public decimal TotalRevenue { get; init; }
        public IEnumerable<BookingSummaryDto> RecentBookings { get; init; } = Enumerable.Empty<BookingSummaryDto>();
    }

    public class GetBookingAnalyticsQueryHandler : IQueryHandler<GetBookingAnalyticsQuery, BookingAnalyticsResponse>
    {
        private readonly IBookingQueryService _bookingQueryService;

        public GetBookingAnalyticsQueryHandler(IBookingQueryService bookingQueryService)
        {
            _bookingQueryService = bookingQueryService;
        }

        public async Task<Result<BookingAnalyticsResponse>> Handle(GetBookingAnalyticsQuery request, CancellationToken cancellationToken)
        {
            // Use the specialized query service for complex operations
            var statistics = await _bookingQueryService.GetBookingStatisticsAsync(request.ApartmentId, cancellationToken);
            var totalRevenue = await _bookingQueryService.GetTotalRevenueAsync(request.ApartmentId, request.FromDate, request.ToDate, cancellationToken);

            // Get recent bookings (example of combining multiple queries)
            var recentBookings = request.ApartmentId.HasValue
                ? await _bookingQueryService.GetApartmentBookingsAsync(request.ApartmentId.Value, request.FromDate, request.ToDate, cancellationToken)
                : Enumerable.Empty<BookingSummaryDto>();

            var response = new BookingAnalyticsResponse
            {
                Statistics = statistics,
                TotalRevenue = totalRevenue,
                RecentBookings = recentBookings.Take(10) // Limit to recent 10
            };

            return Result.Success(response);
        }
    }

    /// <summary>
    /// Example 2: Using the base DapperRepository for custom queries
    /// </summary>
    public class CustomBookingRepository : DapperRepository
    {
        public CustomBookingRepository(ISqlConnectionFactory connectionFactory) : base(connectionFactory)
        {
        }

        /// <summary>
        /// Example of a complex custom query using the base repository
        /// </summary>
        public async Task<IEnumerable<MonthlyRevenueDto>> GetMonthlyRevenueAsync(int year, CancellationToken cancellationToken = default)
        {
            const string sql = """
                SELECT 
                    EXTRACT(MONTH FROM b.created_on_utc) AS Month,
                    EXTRACT(YEAR FROM b.created_on_utc) AS Year,
                    COUNT(*) AS BookingCount,
                    SUM(b.total_price_amount) AS TotalRevenue,
                    AVG(b.total_price_amount) AS AverageBookingValue
                FROM bookings b
                WHERE EXTRACT(YEAR FROM b.created_on_utc) = @Year
                  AND b.status = @CompletedStatus
                GROUP BY EXTRACT(MONTH FROM b.created_on_utc), EXTRACT(YEAR FROM b.created_on_utc)
                ORDER BY Month
                """;

            return await QueryAsync<MonthlyRevenueDto>(sql, new { Year = year, CompletedStatus = 4 }, cancellationToken);
        }

        /// <summary>
        /// Example of using dynamic query building
        /// </summary>
        public async Task<IEnumerable<BookingSearchResultDto>> SearchBookingsAsync(BookingSearchCriteria criteria, CancellationToken cancellationToken = default)
        {
            var queryBuilder = new DynamicQueryBuilder()
                .Select("""
                    b.id AS BookingId,
                    b.status AS Status,
                    b.duration_start AS StartDate,
                    b.duration_end AS EndDate,
                    b.total_price_amount AS TotalAmount,
                    u.first_name AS UserFirstName,
                    u.last_name AS UserLastName,
                    a.name AS ApartmentName
                    """)
                .From("bookings b")
                .Join("INNER JOIN users u ON b.user_id = u.id")
                .Join("INNER JOIN apartments a ON b.apartment_id = a.id")
                .WhereIf(criteria.UserId.HasValue, "b.user_id = @UserId", criteria.UserId)
                .WhereIf(criteria.ApartmentId.HasValue, "b.apartment_id = @ApartmentId", criteria.ApartmentId)
                .WhereIf(criteria.Status.HasValue, "b.status = @Status", (int?)criteria.Status)
                .WhereIf(criteria.FromDate.HasValue, "b.duration_start >= @FromDate", criteria.FromDate)
                .WhereIf(criteria.ToDate.HasValue, "b.duration_end <= @ToDate", criteria.ToDate)
                .OrderBy("b.created_on_utc DESC")
                .Limit(criteria.PageSize ?? 50, criteria.PageNumber);

            var (sql, parameters) = queryBuilder.Build();
            return await QueryAsync<BookingSearchResultDto>(sql, parameters, cancellationToken);
        }

        /// <summary>
        /// Example of bulk operations
        /// </summary>
        public async Task<int> BulkUpdateBookingStatusAsync(IEnumerable<Guid> bookingIds, int newStatus, CancellationToken cancellationToken = default)
        {
            using var connection = _connectionFactory.CreateConnection();
            
            const string sql = """
                UPDATE bookings 
                SET status = @NewStatus, 
                    confirmed_on_utc = CASE WHEN @NewStatus = 2 THEN @UpdatedAt ELSE confirmed_on_utc END,
                    completed_on_utc = CASE WHEN @NewStatus = 4 THEN @UpdatedAt ELSE completed_on_utc END,
                    cancelled_on_utc = CASE WHEN @NewStatus = 5 THEN @UpdatedAt ELSE cancelled_on_utc END
                WHERE id = ANY(@BookingIds)
                """;

            return await connection.ExecuteAsync(sql, new 
            { 
                NewStatus = newStatus, 
                BookingIds = bookingIds.ToArray(),
                UpdatedAt = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// Example 3: Using performance monitoring
    /// </summary>
    public class MonitoredBookingService
    {
        private readonly ISqlConnectionFactory _connectionFactory;
        private readonly IDapperPerformanceMonitor _performanceMonitor;

        public MonitoredBookingService(ISqlConnectionFactory connectionFactory, IDapperPerformanceMonitor performanceMonitor)
        {
            _connectionFactory = connectionFactory;
            _performanceMonitor = performanceMonitor;
        }

        public async Task<IEnumerable<BookingSummaryDto>> GetUserBookingsWithMonitoringAsync(Guid userId, CancellationToken cancellationToken = default)
        {
            const string sql = """
                SELECT 
                    b.id AS BookingId,
                    b.status AS Status,
                    b.duration_start AS StartDate,
                    b.duration_end AS EndDate,
                    b.total_price_amount AS TotalAmount,
                    b.total_price_currency AS Currency,
                    a.name AS ApartmentName
                FROM bookings b
                INNER JOIN apartments a ON b.apartment_id = a.id
                WHERE b.user_id = @UserId
                ORDER BY b.created_on_utc DESC
                """;

            // Monitor the query performance
            return await _performanceMonitor.MonitorQueryAsync(
                "GetUserBookings",
                sql,
                async () =>
                {
                    using var connection = _connectionFactory.CreateConnection();
                    return await connection.QueryAsync<BookingSummaryDto>(sql, new { UserId = userId });
                },
                new { UserId = userId }
            );
        }
    }

    /// <summary>
    /// Example 4: Using Dapper extensions for advanced operations
    /// </summary>
    public class AdvancedBookingOperations
    {
        private readonly ISqlConnectionFactory _connectionFactory;

        public AdvancedBookingOperations(ISqlConnectionFactory connectionFactory)
        {
            _connectionFactory = connectionFactory;
        }

        /// <summary>
        /// Example of using bulk insert with Dapper extensions
        /// </summary>
        public async Task<int> BulkInsertBookingNotificationsAsync(IEnumerable<BookingNotificationDto> notifications, CancellationToken cancellationToken = default)
        {
            using var connection = _connectionFactory.CreateConnection();
            return await connection.BulkInsertAsync("booking_notifications", notifications);
        }

        /// <summary>
        /// Example of using transaction support
        /// </summary>
        public async Task ProcessBookingBatchAsync(IEnumerable<Guid> bookingIds, CancellationToken cancellationToken = default)
        {
            using var connection = _connectionFactory.CreateConnection();
            
            await DatabaseUtilities.ExecuteInTransactionAsync(connection, async (conn, transaction) =>
            {
                // Update booking statuses
                const string updateSql = "UPDATE bookings SET status = @Status WHERE id = ANY(@BookingIds)";
                await conn.ExecuteAsync(updateSql, new { Status = 2, BookingIds = bookingIds.ToArray() }, transaction);

                // Insert audit records
                const string auditSql = "INSERT INTO booking_audit (booking_id, action, created_at) VALUES (@BookingId, @Action, @CreatedAt)";
                var auditRecords = bookingIds.Select(id => new { BookingId = id, Action = "CONFIRMED", CreatedAt = DateTime.UtcNow });
                await conn.ExecuteAsync(auditSql, auditRecords, transaction);
            });
        }

        /// <summary>
        /// Example of using retry logic
        /// </summary>
        public async Task<BookingDetailsDto?> GetBookingWithRetryAsync(Guid bookingId, CancellationToken cancellationToken = default)
        {
            using var connection = _connectionFactory.CreateConnection();
            
            const string sql = """
                SELECT 
                    b.id AS BookingId,
                    b.status AS Status,
                    b.total_price_amount AS TotalAmount,
                    u.email AS UserEmail,
                    a.name AS ApartmentName
                FROM bookings b
                INNER JOIN users u ON b.user_id = u.id
                INNER JOIN apartments a ON b.apartment_id = a.id
                WHERE b.id = @BookingId
                """;

            return await connection.QueryWithRetryAsync<BookingDetailsDto>(
                sql, 
                new { BookingId = bookingId },
                maxRetries: 3,
                retryDelay: TimeSpan.FromMilliseconds(500)
            );
        }
    }

    // Supporting DTOs
    public class MonthlyRevenueDto
    {
        public int Month { get; set; }
        public int Year { get; set; }
        public int BookingCount { get; set; }
        public decimal TotalRevenue { get; set; }
        public decimal AverageBookingValue { get; set; }
    }

    public class BookingSearchCriteria
    {
        public Guid? UserId { get; set; }
        public Guid? ApartmentId { get; set; }
        public int? Status { get; set; }
        public DateOnly? FromDate { get; set; }
        public DateOnly? ToDate { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
    }

    public class BookingSearchResultDto
    {
        public Guid BookingId { get; set; }
        public int Status { get; set; }
        public DateOnly StartDate { get; set; }
        public DateOnly EndDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string UserFirstName { get; set; } = string.Empty;
        public string UserLastName { get; set; } = string.Empty;
        public string ApartmentName { get; set; } = string.Empty;
    }

    public class BookingNotificationDto
    {
        public Guid BookingId { get; set; }
        public string NotificationType { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public bool IsSent { get; set; }
    }
}
